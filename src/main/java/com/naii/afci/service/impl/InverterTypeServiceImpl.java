package com.naii.afci.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.naii.afci.domain.InverterType;
import com.naii.afci.service.InverterTypeService;
import com.naii.afci.mapper.InverterTypeMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【inverter_type(inverter_type;逆变器型号表)】的数据库操作Service实现
* @createDate 2025-08-15 17:18:30
*/
@Service
public class InverterTypeServiceImpl extends ServiceImpl<InverterTypeMapper, InverterType>
    implements InverterTypeService{

}




