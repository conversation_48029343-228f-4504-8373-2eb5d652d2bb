package com.naii.afci.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.naii.afci.domain.Result;
import com.naii.afci.service.ResultService;
import com.naii.afci.mapper.ResultMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【result(result;结果表)】的数据库操作Service实现
* @createDate 2025-08-15 20:51:33
*/
@Service
public class ResultServiceImpl extends ServiceImpl<ResultMapper, Result>
    implements ResultService{

}




