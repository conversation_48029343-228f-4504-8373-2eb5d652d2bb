package com.naii.afci.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * result;结果表
 * @TableName result
 */
@TableName(value ="result")
@Data
public class Result {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 拉弧数据主键
     */
    private Long arcDataId;

    /**
     * 模型主键
     */
    private Long modelId;

    /**
     * 是否发生拉弧（0：未发生，1：发生）
     */
    private Integer isArcing;

    /**
     * 置信度
     */
    private Integer confidence;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Result other = (Result) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getArcDataId() == null ? other.getArcDataId() == null : this.getArcDataId().equals(other.getArcDataId()))
            && (this.getModelId() == null ? other.getModelId() == null : this.getModelId().equals(other.getModelId()))
            && (this.getIsArcing() == null ? other.getIsArcing() == null : this.getIsArcing().equals(other.getIsArcing()))
            && (this.getConfidence() == null ? other.getConfidence() == null : this.getConfidence().equals(other.getConfidence()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getArcDataId() == null) ? 0 : getArcDataId().hashCode());
        result = prime * result + ((getModelId() == null) ? 0 : getModelId().hashCode());
        result = prime * result + ((getIsArcing() == null) ? 0 : getIsArcing().hashCode());
        result = prime * result + ((getConfidence() == null) ? 0 : getConfidence().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", arcDataId=").append(arcDataId);
        sb.append(", modelId=").append(modelId);
        sb.append(", isArcing=").append(isArcing);
        sb.append(", confidence=").append(confidence);
        sb.append("]");
        return sb.toString();
    }
}