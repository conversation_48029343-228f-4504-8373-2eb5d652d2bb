package com.naii.afci.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.*;

/**
 * arc_data;拉弧数据
 * @TableName arc_data
 */
@TableName(value ="arc_data")
@Data
public class ArcData {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 逆变器主键
     */
    private Long inverterId;

    /**
     * 路数
     */
    private Integer channel;

    /**
     * 数据
     */
    private Object data;

    /**
     * 时间
     */
    private Date time;

}