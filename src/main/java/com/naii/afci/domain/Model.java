package com.naii.afci.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * model;模型表
 * @TableName model
 */
@TableName(value ="model")
@Data
public class Model {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 逆变器类型主键
     */
    private Long invertTypeId;

    /**
     * 模型名称
     */
    private String name;

    /**
     * 模型版本
     */
    private String version;

    /**
     * 模型路径（MinIO路径）
     */
    private String path;

    /**
     * 模型类型（0：云端；1：边端）
     */
    private Integer type;

    /**
     * 模型准确度
     */
    private Integer accuracy;

    /**
     * 模型漏报率
     */
    private Integer falseNegativeRate;

    /**
     * 描述
     */
    private String description;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Model other = (Model) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInvertTypeId() == null ? other.getInvertTypeId() == null : this.getInvertTypeId().equals(other.getInvertTypeId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getPath() == null ? other.getPath() == null : this.getPath().equals(other.getPath()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getAccuracy() == null ? other.getAccuracy() == null : this.getAccuracy().equals(other.getAccuracy()))
            && (this.getFalseNegativeRate() == null ? other.getFalseNegativeRate() == null : this.getFalseNegativeRate().equals(other.getFalseNegativeRate()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInvertTypeId() == null) ? 0 : getInvertTypeId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getPath() == null) ? 0 : getPath().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getAccuracy() == null) ? 0 : getAccuracy().hashCode());
        result = prime * result + ((getFalseNegativeRate() == null) ? 0 : getFalseNegativeRate().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", invertTypeId=").append(invertTypeId);
        sb.append(", name=").append(name);
        sb.append(", version=").append(version);
        sb.append(", path=").append(path);
        sb.append(", type=").append(type);
        sb.append(", accuracy=").append(accuracy);
        sb.append(", falseNegativeRate=").append(falseNegativeRate);
        sb.append(", description=").append(description);
        sb.append("]");
        return sb.toString();
    }
}