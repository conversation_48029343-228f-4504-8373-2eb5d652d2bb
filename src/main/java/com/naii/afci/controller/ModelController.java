package com.naii.afci.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.naii.afci.common.Result;
import com.naii.afci.domain.Model;
import com.naii.afci.service.ModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型管理控制器
 */
@Tag(name = "模型管理", description = "AI模型的增删改查接口")
@RestController
@RequestMapping("/api/model")
@RequiredArgsConstructor
public class ModelController {

    private final ModelService modelService;

    @Operation(summary = "新增模型", description = "创建新的AI模型")
    @PostMapping
    public Result<Model> create(@RequestBody Model model) {
        boolean success = modelService.save(model);
        if (success) {
            return Result.success("模型创建成功", model);
        }
        return Result.error("模型创建失败");
    }

    @Operation(summary = "根据ID删除模型", description = "根据ID删除指定的AI模型")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {
        boolean success = modelService.removeById(id);
        if (success) {
            return Result.success();
        }
        return Result.error("模型删除���败");
    }

    @Operation(summary = "批量删除模型", description = "根据ID列表批量删除AI模型")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<Long> ids) {
        boolean success = modelService.removeByIds(ids);
        if (success) {
            return Result.success();
        }
        return Result.error("模型批量删除失败");
    }

    @Operation(summary = "修改模型", description = "更新AI模型信息")
    @PutMapping
    public Result<Model> update(@RequestBody Model model) {
        boolean success = modelService.updateById(model);
        if (success) {
            return Result.success("模型更新成功", model);
        }
        return Result.error("模型更新失败");
    }

    @Operation(summary = "根据ID查询模型", description = "根据ID获取AI模型详细信息")
    @GetMapping("/{id}")
    public Result<Model> getById(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {
        Model model = modelService.getById(id);
        if (model != null) {
            return Result.success(model);
        }
        return Result.error("模型不存在");
    }

    @Operation(summary = "查询所有模型", description = "获取所有AI模型列表")
    @GetMapping("/list")
    public Result<List<Model>> list() {
        List<Model> list = modelService.list();
        return Result.success(list);
    }

    @Operation(summary = "分页查询模型", description = "分页获取AI模型列表")
    @GetMapping("/page")
    public Result<Page<Model>> page(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "逆变器类型ID")
            @RequestParam(required = false) Long invertTypeId,
            @Parameter(description = "模型名称关键字")
            @RequestParam(required = false) String name,
            @Parameter(description = "模型版本")
            @RequestParam(required = false) String version,
            @Parameter(description = "模型类型 (0:云端, 1:边端)")
            @RequestParam(required = false) Integer type,
            @Parameter(description = "最小准确度")
            @RequestParam(required = false) Integer minAccuracy) {

        Page<Model> page = new Page<>(current, size);
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();

        if (invertTypeId != null) {
            queryWrapper.eq("invert_type_id", invertTypeId);
        }
        if (name != null && !name.trim().isEmpty()) {
            queryWrapper.like("name", name);
        }
        if (version != null && !version.trim().isEmpty()) {
            queryWrapper.eq("version", version);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        if (minAccuracy != null) {
            queryWrapper.ge("accuracy", minAccuracy);
        }

        queryWrapper.orderByDesc("accuracy").orderByDesc("id");

        Page<Model> result = modelService.page(page, queryWrapper);
        return Result.success(result);
    }

    @Operation(summary = "根据逆变器类型查询模型", description = "获取指定逆变器类型的所有模型")
    @GetMapping("/by-inverter-type/{invertTypeId}")
    public Result<List<Model>> getByInvertTypeId(
            @Parameter(description = "逆变器类型ID", required = true)
            @PathVariable Long invertTypeId) {
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invert_type_id", invertTypeId)
                   .orderByDesc("accuracy");
        List<Model> list = modelService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据模型类型查询", description = "获取指定类型的所有模型")
    @GetMapping("/by-type/{type}")
    public Result<List<Model>> getByType(
            @Parameter(description = "模型类型 (0:云端, 1:边端)", required = true)
            @PathVariable Integer type) {
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type)
                   .orderByDesc("accuracy");
        List<Model> list = modelService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据名称和版本查询", description = "根据模型名称和版本精确查询")
    @GetMapping("/by-name-version")
    public Result<Model> getByNameAndVersion(
            @Parameter(description = "模型名称", required = true)
            @RequestParam String name,
            @Parameter(description = "模型版本", required = true)
            @RequestParam String version) {
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name).eq("version", version);
        Model model = modelService.getOne(queryWrapper);
        if (model != null) {
            return Result.success(model);
        }
        return Result.error("指定名称和版本的模型不存在");
    }

    @Operation(summary = "获取最高准确度模型", description = "获取指定逆变器类型的最高准确度模型")
    @GetMapping("/best-accuracy/{invertTypeId}")
    public Result<Model> getBestAccuracy(
            @Parameter(description = "逆变器类型ID", required = true)
            @PathVariable Long invertTypeId,
            @Parameter(description = "模型类型 (0:云端, 1:边端)")
            @RequestParam(required = false) Integer type) {
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invert_type_id", invertTypeId);

        if (type != null) {
            queryWrapper.eq("type", type);
        }

        queryWrapper.orderByDesc("accuracy").last("LIMIT 1");
        Model model = modelService.getOne(queryWrapper);

        if (model != null) {
            return Result.success(model);
        }
        return Result.error("未找到符合条件的模型");
    }

    @Operation(summary = "根据准确度范围查询", description = "获取指定准确度范围内的模型")
    @GetMapping("/by-accuracy-range")
    public Result<List<Model>> getByAccuracyRange(
            @Parameter(description = "最小准确度", required = true)
            @RequestParam Integer minAccuracy,
            @Parameter(description = "最大准确度", required = true)
            @RequestParam Integer maxAccuracy,
            @Parameter(description = "逆变器类型ID")
            @RequestParam(required = false) Long invertTypeId,
            @Parameter(description = "模型类型 (0:云端, 1:边端)")
            @RequestParam(required = false) Integer type) {
        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("accuracy", minAccuracy, maxAccuracy);

        if (invertTypeId != null) {
            queryWrapper.eq("invert_type_id", invertTypeId);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }

        queryWrapper.orderByDesc("accuracy");
        List<Model> list = modelService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "统计模型数量", description = "统计指定条件下的模型总数")
    @GetMapping("/count")
    public Result<Long> count(
            @Parameter(description = "逆变器类型ID")
            @RequestParam(required = false) Long invertTypeId,
            @Parameter(description = "模型类型 (0:云端, 1:边端)")
            @RequestParam(required = false) Integer type,
            @Parameter(description = "最小准确度")
            @RequestParam(required = false) Integer minAccuracy) {

        QueryWrapper<Model> queryWrapper = new QueryWrapper<>();

        if (invertTypeId != null) {
            queryWrapper.eq("invert_type_id", invertTypeId);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        if (minAccuracy != null) {
            queryWrapper.ge("accuracy", minAccuracy);
        }

        long count = modelService.count(queryWrapper);
        return Result.success(count);
    }

    @Operation(summary = "更新模型路径", description = "更新指定模型的存储路径")
    @PutMapping("/{id}/path")
    public Result<Void> updatePath(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "新路径", required = true)
            @RequestParam String path) {
        Model model = new Model();
        model.setId(id);
        model.setPath(path);
        boolean success = modelService.updateById(model);
        if (success) {
            return Result.success();
        }
        return Result.error("模型路径更新失败");
    }
}
