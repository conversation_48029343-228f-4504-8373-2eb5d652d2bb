package com.naii.afci.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.naii.afci.common.Result;
import com.naii.afci.service.ResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 结果管理控制器
 */
@Tag(name = "结果管理", description = "拉弧检测结��的增删改查接口")
@RestController
@RequestMapping("/api/result")
@RequiredArgsConstructor
public class ResultController {

    private final ResultService resultService;

    @Operation(summary = "新增检测结果", description = "创建新的拉弧检测结果")
    @PostMapping
    public Result<com.naii.afci.domain.Result> create(@RequestBody com.naii.afci.domain.Result result) {
        boolean success = resultService.save(result);
        if (success) {
            return Result.success("检测结果创建成功", result);
        }
        return Result.error("检测结果创建失败");
    }

    @Operation(summary = "根据ID删除检测结果", description = "根据ID删除指定的检测结果")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "检测结果ID", required = true)
            @PathVariable Long id) {
        boolean success = resultService.removeById(id);
        if (success) {
            return Result.success();
        }
        return Result.error("检测结果删除失败");
    }

    @Operation(summary = "批量删除检测结果", description = "根据ID列表批量删除检测结果")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<Long> ids) {
        boolean success = resultService.removeByIds(ids);
        if (success) {
            return Result.success();
        }
        return Result.error("检测结果批量删除失败");
    }

    @Operation(summary = "修改检测结果", description = "更新检测结果信息")
    @PutMapping
    public Result<com.naii.afci.domain.Result> update(@RequestBody com.naii.afci.domain.Result result) {
        boolean success = resultService.updateById(result);
        if (success) {
            return Result.success("检测结果更新成功", result);
        }
        return Result.error("检测结果更新失败");
    }

    @Operation(summary = "根据ID查询检测结果", description = "根据ID获取检测结果详细信息")
    @GetMapping("/{id}")
    public Result<com.naii.afci.domain.Result> getById(
            @Parameter(description = "检测结果ID", required = true)
            @PathVariable Long id) {
        com.naii.afci.domain.Result result = resultService.getById(id);
        if (result != null) {
            return Result.success(result);
        }
        return Result.error("检测结果不存在");
    }

    @Operation(summary = "查询所有检测结果", description = "获取所有检测结果列表")
    @GetMapping("/list")
    public Result<List<com.naii.afci.domain.Result>> list() {
        List<com.naii.afci.domain.Result> list = resultService.list();
        return Result.success(list);
    }

    @Operation(summary = "分页查询检测结果", description = "分页获取检测结果列表")
    @GetMapping("/page")
    public Result<Page<com.naii.afci.domain.Result>> page(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "拉弧数据ID")
            @RequestParam(required = false) Long arcDataId,
            @Parameter(description = "模型ID")
            @RequestParam(required = false) Long modelId,
            @Parameter(description = "是否发生拉弧 (0:未发生, 1:发生)")
            @RequestParam(required = false) Integer isArcing,
            @Parameter(description = "最小置信度")
            @RequestParam(required = false) Integer minConfidence) {

        Page<com.naii.afci.domain.Result> page = new Page<>(current, size);
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();

        if (arcDataId != null) {
            queryWrapper.eq("arc_data_id", arcDataId);
        }
        if (modelId != null) {
            queryWrapper.eq("model_id", modelId);
        }
        if (isArcing != null) {
            queryWrapper.eq("is_arcing", isArcing);
        }
        if (minConfidence != null) {
            queryWrapper.ge("confidence", minConfidence);
        }

        queryWrapper.orderByDesc("id");

        Page<com.naii.afci.domain.Result> result = resultService.page(page, queryWrapper);
        return Result.success(result);
    }

    @Operation(summary = "根据拉弧数据ID查询结果", description = "获取指定拉弧数据的所有检测结果")
    @GetMapping("/by-arc-data/{arcDataId}")
    public Result<List<com.naii.afci.domain.Result>> getByArcDataId(
            @Parameter(description = "拉弧数据ID", required = true)
            @PathVariable Long arcDataId) {
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("arc_data_id", arcDataId).orderByDesc("confidence");
        List<com.naii.afci.domain.Result> list = resultService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据模型ID查询结果", description = "获取指定模型的所有检测结果")
    @GetMapping("/by-model/{modelId}")
    public Result<List<com.naii.afci.domain.Result>> getByModelId(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long modelId) {
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_id", modelId).orderByDesc("id");
        List<com.naii.afci.domain.Result> list = resultService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据拉弧状态查询", description = "获取指定拉弧状态的所有检测结果")
    @GetMapping("/by-arcing-status/{isArcing}")
    public Result<List<com.naii.afci.domain.Result>> getByArcingStatus(
            @Parameter(description = "是否发生拉弧 (0:未发生, 1:发生)", required = true)
            @PathVariable Integer isArcing) {
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_arcing", isArcing).orderByDesc("confidence");
        List<com.naii.afci.domain.Result> list = resultService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据置信度范围查询", description = "获取指定置信度范围内的检测结果")
    @GetMapping("/by-confidence-range")
    public Result<List<com.naii.afci.domain.Result>> getByConfidenceRange(
            @Parameter(description = "最小置信度", required = true)
            @RequestParam Integer minConfidence,
            @Parameter(description = "最大置信度", required = true)
            @RequestParam Integer maxConfidence,
            @Parameter(description = "是否发生拉弧 (0:未发生, 1:发生)")
            @RequestParam(required = false) Integer isArcing,
            @Parameter(description = "模型ID")
            @RequestParam(required = false) Long modelId) {
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("confidence", minConfidence, maxConfidence);

        if (isArcing != null) {
            queryWrapper.eq("is_arcing", isArcing);
        }
        if (modelId != null) {
            queryWrapper.eq("model_id", modelId);
        }

        queryWrapper.orderByDesc("confidence");
        List<com.naii.afci.domain.Result> list = resultService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "获取高置信度拉弧结果", description = "获取高置信度的拉弧检测结果")
    @GetMapping("/high-confidence-arcing")
    public Result<List<com.naii.afci.domain.Result>> getHighConfidenceArcing(
            @Parameter(description = "最小置信度阈值", example = "80")
            @RequestParam(defaultValue = "80") Integer threshold,
            @Parameter(description = "模型ID")
            @RequestParam(required = false) Long modelId) {
        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_arcing", 1)
                   .ge("confidence", threshold);

        if (modelId != null) {
            queryWrapper.eq("model_id", modelId);
        }

        queryWrapper.orderByDesc("confidence");
        List<com.naii.afci.domain.Result> list = resultService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "统计检测结果", description = "统计指定条件下的检测结果数量")
    @GetMapping("/statistics")
    public Result<Object> getStatistics(
            @Parameter(description = "模型ID")
            @RequestParam(required = false) Long modelId,
            @Parameter(description = "最小置信度")
            @RequestParam(required = false) Integer minConfidence) {

        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();

        if (modelId != null) {
            queryWrapper.eq("model_id", modelId);
        }
        if (minConfidence != null) {
            queryWrapper.ge("confidence", minConfidence);
        }

        // 总数统计
        long totalCount = resultService.count(queryWrapper);

        // 拉弧检测统计
        QueryWrapper<com.naii.afci.domain.Result> arcingQuery = queryWrapper.clone();
        arcingQuery.eq("is_arcing", 1);
        long arcingCount = resultService.count(arcingQuery);

        // 正常检测统计
        QueryWrapper<com.naii.afci.domain.Result> normalQuery = queryWrapper.clone();
        normalQuery.eq("is_arcing", 0);
        long normalCount = resultService.count(normalQuery);

        // 构建统计结果
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalCount", totalCount);
        statistics.put("arcingCount", arcingCount);
        statistics.put("normalCount", normalCount);
        statistics.put("arcingRate", totalCount > 0 ? (double) arcingCount / totalCount * 100 : 0.0);

        return Result.success(statistics);
    }

    @Operation(summary = "获取模型准确性统计", description = "获取指定模型的检测准确性统计信息")
    @GetMapping("/model-accuracy/{modelId}")
    public Result<Object> getModelAccuracy(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long modelId,
            @Parameter(description = "最小置信度阈值", example = "70")
            @RequestParam(defaultValue = "70") Integer threshold) {

        QueryWrapper<com.naii.afci.domain.Result> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_id", modelId).ge("confidence", threshold);

        List<com.naii.afci.domain.Result> results = resultService.list(queryWrapper);

        long totalCount = results.size();
        long highConfidenceCount = results.stream()
                .mapToLong(r -> r.getConfidence() >= 90 ? 1 : 0)
                .sum();

        double avgConfidence = results.stream()
                .mapToInt(com.naii.afci.domain.Result::getConfidence)
                .average()
                .orElse(0.0);

        java.util.Map<String, Object> accuracy = new java.util.HashMap<>();
        accuracy.put("modelId", modelId);
        accuracy.put("totalPredictions", totalCount);
        accuracy.put("highConfidenceCount", highConfidenceCount);
        accuracy.put("averageConfidence", Math.round(avgConfidence * 100.0) / 100.0);
        accuracy.put("highConfidenceRate", totalCount > 0 ? (double) highConfidenceCount / totalCount * 100 : 0.0);

        return Result.success(accuracy);
    }
}
