package com.naii.afci.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.naii.afci.common.Result;
import com.naii.afci.domain.InverterType;
import com.naii.afci.service.InverterTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 逆变器型号管理控制器
 */
@Tag(name = "逆变器型号管理", description = "逆变器型号的增删改查接口")
@RestController
@RequestMapping("/api/inverter-type")
@RequiredArgsConstructor
public class InverterTypeController {

    private final InverterTypeService inverterTypeService;

    @Operation(summary = "新增逆变器型号", description = "创建新的逆变器型号")
    @PostMapping
    public Result<InverterType> create(@RequestBody InverterType inverterType) {
        boolean success = inverterTypeService.save(inverterType);
        if (success) {
            return Result.success("逆变器型号创建成功", inverterType);
        }
        return Result.error("逆变器型号创建失败");
    }

    @Operation(summary = "根据ID删除逆变器型号", description = "根据ID删除指定的逆变器型号")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "逆变器型号ID", required = true)
            @PathVariable Long id) {
        boolean success = inverterTypeService.removeById(id);
        if (success) {
            return Result.success();
        }
        return Result.error("逆变器型号删除失败");
    }

    @Operation(summary = "批量删除逆变器型号", description = "根据ID列表批量删除逆变器型号")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<Long> ids) {
        boolean success = inverterTypeService.removeByIds(ids);
        if (success) {
            return Result.success();
        }
        return Result.error("逆变器型号批量删除失败");
    }

    @Operation(summary = "修改逆变器型号", description = "更新逆变器型号信息")
    @PutMapping
    public Result<InverterType> update(@RequestBody InverterType inverterType) {
        boolean success = inverterTypeService.updateById(inverterType);
        if (success) {
            return Result.success("逆变器型号更新成功", inverterType);
        }
        return Result.error("逆变器型号更新失败");
    }

    @Operation(summary = "根据ID查询逆变器型号", description = "根据ID获取逆变器型号详细信息")
    @GetMapping("/{id}")
    public Result<InverterType> getById(
            @Parameter(description = "逆变器型号ID", required = true)
            @PathVariable Long id) {
        InverterType inverterType = inverterTypeService.getById(id);
        if (inverterType != null) {
            return Result.success(inverterType);
        }
        return Result.error("逆变器型号不存在");
    }

    @Operation(summary = "查询所有逆变器型号", description = "获取所有逆变器型号列表")
    @GetMapping("/list")
    public Result<List<InverterType>> list() {
        List<InverterType> list = inverterTypeService.list();
        return Result.success(list);
    }

    @Operation(summary = "分页查询逆变器型号", description = "分页获取逆变器型号列表")
    @GetMapping("/page")
    public Result<Page<InverterType>> page(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "型号关键字")
            @RequestParam(required = false) String type,
            @Parameter(description = "描述关键字")
            @RequestParam(required = false) String description) {

        Page<InverterType> page = new Page<>(current, size);
        QueryWrapper<InverterType> queryWrapper = new QueryWrapper<>();

        if (type != null && !type.trim().isEmpty()) {
            queryWrapper.like("type", type);
        }
        if (description != null && !description.trim().isEmpty()) {
            queryWrapper.like("description", description);
        }

        Page<InverterType> result = inverterTypeService.page(page, queryWrapper);
        return Result.success(result);
    }

    @Operation(summary = "根据型号查询", description = "根据型号精确查询逆变器型号")
    @GetMapping("/by-type/{type}")
    public Result<InverterType> getByType(
            @Parameter(description = "逆变器型号", required = true)
            @PathVariable String type) {
        QueryWrapper<InverterType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        InverterType inverterType = inverterTypeService.getOne(queryWrapper);
        if (inverterType != null) {
            return Result.success(inverterType);
        }
        return Result.error("指定型号的逆变器不存在");
    }
}
