package com.naii.afci.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.naii.afci.common.Result;
import com.naii.afci.domain.ArcData;
import com.naii.afci.service.ArcDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 拉弧数据管理控制器
 */
@Tag(name = "拉弧数据管理", description = "拉弧数据的增删改查接口")
@RestController
@RequestMapping("/api/arc-data")
@RequiredArgsConstructor
public class ArcDataController {

    private final ArcDataService arcDataService;

    @Operation(summary = "新增拉弧数据", description = "创建新的拉弧数据记录")
    @PostMapping
    public Result<ArcData> create(@RequestBody ArcData arcData) {
        boolean success = arcDataService.save(arcData);
        if (success) {
            return Result.success("拉弧数据创建成功", arcData);
        }
        return Result.error("拉弧数据创建失败");
    }

    @Operation(summary = "根据ID删除拉弧数据", description = "根据ID删除指定的拉弧数据记录")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "拉弧数据ID", required = true)
            @PathVariable Long id) {
        boolean success = arcDataService.removeById(id);
        if (success) {
            return Result.success();
        }
        return Result.error("拉弧数据删除失败");
    }

    @Operation(summary = "批量删除拉弧数据", description = "根据ID列表批量删除拉弧数据记录")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<Long> ids) {
        boolean success = arcDataService.removeByIds(ids);
        if (success) {
            return Result.success();
        }
        return Result.error("拉弧数据批量删除失败");
    }

    @Operation(summary = "修改拉弧数据", description = "更新拉弧数据记录信息")
    @PutMapping
    public Result<ArcData> update(@RequestBody ArcData arcData) {
        boolean success = arcDataService.updateById(arcData);
        if (success) {
            return Result.success("拉弧数据更新成功", arcData);
        }
        return Result.error("拉弧数据更新失败");
    }

    @Operation(summary = "根据ID查询拉弧数据", description = "根据ID获取拉弧数据详细信息")
    @GetMapping("/{id}")
    public Result<ArcData> getById(
            @Parameter(description = "拉弧数据ID", required = true)
            @PathVariable Long id) {
        ArcData arcData = arcDataService.getById(id);
        if (arcData != null) {
            return Result.success(arcData);
        }
        return Result.error("拉弧数据不存在");
    }

    @Operation(summary = "查询所有拉弧数据", description = "获取所有拉弧数据列表")
    @GetMapping("/list")
    public Result<List<ArcData>> list() {
        List<ArcData> list = arcDataService.list();
        return Result.success(list);
    }

    @Operation(summary = "分页查询拉弧数据", description = "分页获取拉弧数据列表")
    @GetMapping("/page")
    public Result<Page<ArcData>> page(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "逆变器ID")
            @RequestParam(required = false) Long inverterId,
            @Parameter(description = "路数")
            @RequestParam(required = false) Integer channel,
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        Page<ArcData> page = new Page<>(current, size);
        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();

        if (inverterId != null) {
            queryWrapper.eq("inverter_id", inverterId);
        }
        if (channel != null) {
            queryWrapper.eq("channel", channel);
        }
        if (startTime != null) {
            queryWrapper.ge("time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("time", endTime);
        }

        queryWrapper.orderByDesc("time");

        Page<ArcData> result = arcDataService.page(page, queryWrapper);
        return Result.success(result);
    }

    @Operation(summary = "根据逆变器ID查询拉弧数据", description = "获取指定逆变器的所有拉弧数据")
    @GetMapping("/by-inverter/{inverterId}")
    public Result<List<ArcData>> getByInverterId(
            @Parameter(description = "逆变器ID", required = true)
            @PathVariable Long inverterId) {
        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_id", inverterId).orderByDesc("time");
        List<ArcData> list = arcDataService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据逆变器ID和路数查询", description = "获取指定逆变器和路数的拉弧数据")
    @GetMapping("/by-inverter-channel")
    public Result<List<ArcData>> getByInverterAndChannel(
            @Parameter(description = "逆变器ID", required = true)
            @RequestParam Long inverterId,
            @Parameter(description = "路数", required = true)
            @RequestParam Integer channel) {
        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_id", inverterId)
                   .eq("channel", channel)
                   .orderByDesc("time");
        List<ArcData> list = arcDataService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据时间范围查询", description = "获取指定时间范围内的拉弧数据")
    @GetMapping("/by-time-range")
    public Result<List<ArcData>> getByTimeRange(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @Parameter(description = "逆变器ID")
            @RequestParam(required = false) Long inverterId) {
        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("time", startTime, endTime);

        if (inverterId != null) {
            queryWrapper.eq("inverter_id", inverterId);
        }

        queryWrapper.orderByDesc("time");
        List<ArcData> list = arcDataService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "获取最新拉弧数据", description = "获取指定逆变器的最新拉弧数据")
    @GetMapping("/latest/{inverterId}")
    public Result<ArcData> getLatest(
            @Parameter(description = "逆变器ID", required = true)
            @PathVariable Long inverterId,
            @Parameter(description = "路数")
            @RequestParam(required = false) Integer channel) {
        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_id", inverterId);

        if (channel != null) {
            queryWrapper.eq("channel", channel);
        }

        queryWrapper.orderByDesc("time").last("LIMIT 1");
        ArcData arcData = arcDataService.getOne(queryWrapper);

        if (arcData != null) {
            return Result.success(arcData);
        }
        return Result.error("未找到拉弧数据");
    }

    @Operation(summary = "统计拉弧数据数量", description = "统计指定条件下的拉弧数据总数")
    @GetMapping("/count")
    public Result<Long> count(
            @Parameter(description = "逆变器ID")
            @RequestParam(required = false) Long inverterId,
            @Parameter(description = "路数")
            @RequestParam(required = false) Integer channel,
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        QueryWrapper<ArcData> queryWrapper = new QueryWrapper<>();

        if (inverterId != null) {
            queryWrapper.eq("inverter_id", inverterId);
        }
        if (channel != null) {
            queryWrapper.eq("channel", channel);
        }
        if (startTime != null) {
            queryWrapper.ge("time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("time", endTime);
        }

        long count = arcDataService.count(queryWrapper);
        return Result.success(count);
    }
}
