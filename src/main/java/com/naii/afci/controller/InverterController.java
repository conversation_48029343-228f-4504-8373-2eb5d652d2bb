package com.naii.afci.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.naii.afci.common.Result;
import com.naii.afci.domain.Inverter;
import com.naii.afci.service.InverterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 逆变器管理控制器
 */
@Tag(name = "逆变器管理", description = "逆变器设备的增删改查接口")
@RestController
@RequestMapping("/api/inverter")
@RequiredArgsConstructor
public class InverterController {
    @Resource
    private  InverterService inverterService;

    @Operation(summary = "新增逆变器", description = "创建新的逆变器设备")
    @PostMapping
    public Result<Inverter> create(@RequestBody Inverter inverter) {
        boolean success = inverterService.save(inverter);
        if (success) {
            return Result.success("逆变器创建成功", inverter);
        }
        return Result.error("逆变器创建失败");
    }

    @Operation(summary = "根据ID删除逆变器", description = "根据ID删除指定的逆变器设备")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "逆变器ID", required = true)
            @PathVariable Long id) {
        boolean success = inverterService.removeById(id);
        if (success) {
            return Result.success();
        }
        return Result.error("逆变器删除失败");
    }

    @Operation(summary = "批量删除逆变器", description = "根据ID列表批量删除逆变器设备")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<Long> ids) {
        boolean success = inverterService.removeByIds(ids);
        if (success) {
            return Result.success();
        }
        return Result.error("逆变器��量删除失败");
    }

    @Operation(summary = "修改逆变器", description = "更新逆变器设备信息")
    @PutMapping
    public Result<Inverter> update(@RequestBody Inverter inverter) {
        boolean success = inverterService.updateById(inverter);
        if (success) {
            return Result.success("逆变器更新成功", inverter);
        }
        return Result.error("逆变器更新失败");
    }

    @Operation(summary = "根据ID查询逆变器", description = "根据ID获取逆变器设备详细信息")
    @GetMapping("/{id}")
    public Result<Inverter> getById(
            @Parameter(description = "逆变器ID", required = true)
            @PathVariable Long id) {
        Inverter inverter = inverterService.getById(id);
        if (inverter != null) {
            return Result.success(inverter);
        }
        return Result.error("逆变器不存在");
    }

    @Operation(summary = "查询所有逆变器", description = "获取所有逆变器设备列表")
    @GetMapping("/list")
    public Result<List<Inverter>> list() {
        List<Inverter> list = inverterService.list();
        return Result.success(list);
    }

    @Operation(summary = "分页查询逆变器", description = "分页获取逆变器设备列表")
    @GetMapping("/page")
    public Result<Page<Inverter>> page(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "类型ID")
            @RequestParam(required = false) Long typeId,
            @Parameter(description = "序列号关键字")
            @RequestParam(required = false) String sn,
            @Parameter(description = "状态 (0:正常, 1:异常)")
            @RequestParam(required = false) Integer status) {

        Page<Inverter> page = new Page<>(current, size);
        QueryWrapper<Inverter> queryWrapper = new QueryWrapper<>();

        if (typeId != null) {
            queryWrapper.eq("type_id", typeId);
        }
        if (sn != null && !sn.trim().isEmpty()) {
            queryWrapper.like("sn", sn);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        Page<Inverter> result = inverterService.page(page, queryWrapper);
        return Result.success(result);
    }

    @Operation(summary = "根据序列号查询", description = "根据序列号精确查询逆变器")
    @GetMapping("/by-sn/{sn}")
    public Result<Inverter> getBySn(
            @Parameter(description = "逆变器序列号", required = true)
            @PathVariable String sn) {
        QueryWrapper<Inverter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        Inverter inverter = inverterService.getOne(queryWrapper);
        if (inverter != null) {
            return Result.success(inverter);
        }
        return Result.error("指定序列号的逆变器不存在");
    }

    @Operation(summary = "根据类型ID查询逆变器", description = "获取指定类型的所有逆变器")
    @GetMapping("/by-type/{typeId}")
    public Result<List<Inverter>> getByTypeId(
            @Parameter(description = "逆变器类型ID", required = true)
            @PathVariable Long typeId) {
        QueryWrapper<Inverter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_id", typeId);
        List<Inverter> list = inverterService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "根据状态查询逆变器", description = "获取指定状态的所有逆变器")
    @GetMapping("/by-status/{status}")
    public Result<List<Inverter>> getByStatus(
            @Parameter(description = "状态 (0:正常, 1:异常)", required = true)
            @PathVariable Integer status) {
        QueryWrapper<Inverter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        List<Inverter> list = inverterService.list(queryWrapper);
        return Result.success(list);
    }

    @Operation(summary = "更新逆变器状态", description = "更新指定逆变器的运行状态")
    @PutMapping("/{id}/status")
    public Result<Void> updateStatus(
            @Parameter(description = "逆变器ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "新状态 (0:正常, 1:异常)", required = true)
            @RequestParam Integer status) {
        Inverter inverter = new Inverter();
        inverter.setId(id);
        inverter.setStatus(status);
        boolean success = inverterService.updateById(inverter);
        if (success) {
            return Result.success();
        }
        return Result.error("逆变器状态更新失败");
    }
}
