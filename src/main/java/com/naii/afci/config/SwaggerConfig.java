package com.naii.afci.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger配置类
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("AFCI拉弧故障检测系统API文档")
                        .description("AFCI拉弧故障检测系统的RESTful API接口文档，包含逆变器管理、拉弧数据采集、AI模型管理和检测结果等功能模块")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("NAII技术团队")
                                .email("<EMAIL>")
                                .url("https://www.naii.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("http://springdoc.org")));
    }
}
