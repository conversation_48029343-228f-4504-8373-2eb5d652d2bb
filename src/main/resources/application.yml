spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************
    username: root
    password: 123456
  data:
    redis:
      host: *********
      port: 6379

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.naii.afci.controller
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn