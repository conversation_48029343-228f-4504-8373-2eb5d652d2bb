<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.naii.afci.mapper.InverterMapper">

    <resultMap id="BaseResultMap" type="com.naii.afci.domain.Inverter">
            <id property="id" column="id" />
            <result property="typeId" column="type_id" />
            <result property="sn" column="sn" />
            <result property="status" column="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,type_id,sn,status
    </sql>
</mapper>
