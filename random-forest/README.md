# Java版本随机森林训练模块

本模块是基于Python版本`fft_random_forest.py`实现的Java版本随机森林训练模块，功能完全一致。

## 🎯 功能特性

- ✅ **完全对等的功能**：与Python版本实现相同的算法流程和参数设置
- ✅ **FFT特征提取**：实现短时傅里叶变换，包括汉宁窗、FFT变换、幅度计算等
- ✅ **数据预处理**：支持CSV文件加载、数据分段、文件扫描等功能
- ✅ **随机森林训练**：使用Smile库实现高性能随机森林训练
- ✅ **模型评估**：提供完整的评估指标计算（准确率、精确率、召回率、F1分数等）
- ✅ **单元测试**：包含全面的单元测试和一致性测试

## 📋 核心模块

### 1. TrainingConfiguration
训练配置类，包含所有与Python版本一致的参数：
- 采样频率：250kHz
- 窗口长度：1024
- 随机森林：100棵树，最小叶节点5个样本
- 特征长度：102维

### 2. DataProcessor
数据预处理类：
- `scanDir()` - 递归扫描目录文件
- `loadCsvRobust()` - 健壮的CSV文件加载
- `calculateSegments()` - 计算数据分段数
- `extractSegment()` - 提取数据分段

### 3. FFTFeatureExtractor
FFT特征提取类：
- `hanningWindow()` - 汉宁窗生成
- `fft()` - FFT变换实现
- `mystft()` - 短时傅里叶变换

### 4. RandomForestTrainer
随机森林训练类：
- `trainRandomForest()` - 训练模型
- `trainTestSplit()` - 数据分割
- `predict()` - 模型预测

### 5. ModelEvaluator
模型评估类：
- 混淆矩阵计算
- 准确率、精确率、召回率、F1分数
- 误报率、漏报率计算

### 6. RandomForestService
主服务类，集成所有模块的完整训练流程

## 🚀 使用方法

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 运行测试
```bash
mvn test
```

### 3. 运行训练程序
```bash
# 使用指定数据路径
java -cp target/classes com.naii.afci.RandomForestTrainingMain <故障数据路径> <正常数据路径>

# 示例
java -cp target/classes com.naii.afci.RandomForestTrainingMain D:/data/ARC D:/data/NOR
```

### 4. 演示模式（使用示例数据）
```bash
java -cp target/classes com.naii.afci.RandomForestTrainingMain
```

## 📊 与Python版本的对比

| 功能模块 | Python版本 | Java版本 | 一致性 |
|---------|------------|----------|--------|
| 数据预处理 | `scan_dir`, `load_csv_robust`, `data_pre_process` | `DataProcessor` | ✅ |
| FFT特征提取 | `mystft` | `FFTFeatureExtractor.mystft` | ✅ |
| 随机森林训练 | `RandomForestClassifier` | `RandomForest` (Smile) | ✅ |
| 模型评估 | 混淆矩阵、各种指标计算 | `ModelEvaluator` | ✅ |
| 数据分割 | `train_test_split` | `trainTestSplit` | ✅ |

## 🔧 依赖项

- **Java 21**
- **Smile 2.6.0** - 机器学习库
- **JUnit 5** - 单元测试框架

## 📁 项目结构

```
random-forest/
├── src/main/java/com/naii/afci/
│   ├── RandomForestTrainingMain.java      # 主程序
│   └── service/
│       ├── TrainingConfiguration.java     # 配置类
│       ├── DataProcessor.java            # 数据处理
│       ├── FFTFeatureExtractor.java      # FFT特征提取
│       ├── RandomForestTrainer.java      # 随机森林训练
│       ├── ModelEvaluator.java           # 模型评估
│       └── RandomForestService.java      # 主服务
├── src/test/java/com/naii/afci/service/
│   ├── DataProcessorTest.java            # 数据处理测试
│   ├── FFTFeatureExtractorTest.java      # FFT测试
│   ├── ModelEvaluatorTest.java           # 评估测试
│   ├── RandomForestTrainerTest.java      # 训练测试
│   ├── PythonJavaConsistencyTest.java    # 一致性测试
│   └── SimpleIntegrationTest.java        # 集成测试
└── src/main/resources/edged-tools/
    └── fft_random_forest.py              # 原Python版本
```

## 🧪 测试覆盖

- **单元测试**：每个模块都有对应的单元测试
- **集成测试**：验证整个训练流程
- **一致性测试**：确保Java版本与Python版本输出一致
- **边界测试**：测试各种边界情况和异常处理

## 📈 性能特点

- **高效的FFT实现**：使用Cooley-Tukey算法
- **并行训练**：Smile库支持多线程训练
- **内存优化**：合理的数据结构设计
- **错误处理**：健壮的异常处理机制

## 🔍 故障排除

### 常见问题

1. **编译错误**：确保使用Java 21和正确的Maven配置
2. **内存不足**：对于大数据集，可能需要增加JVM内存：`-Xmx4g`
3. **文件路径问题**：确保数据文件路径正确且可访问

### 调试建议

- 启用详细日志输出
- 使用小数据集进行初步测试
- 检查数据文件格式是否正确

## 📝 许可证

本项目遵循与主项目相同的许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模块。
