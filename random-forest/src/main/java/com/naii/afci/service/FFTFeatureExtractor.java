package com.naii.afci.service;

import java.util.Arrays;

/**
 * FFT特征提取类
 * 实现与Python版本相同的短时傅里叶变换功能
 */
public class FFTFeatureExtractor {
    
    /**
     * 生成汉宁窗
     * 
     * @param length 窗口长度
     * @return 汉宁窗数组
     */
    public double[] hanningWindow(int length) {
        double[] window = new double[length];
        for (int i = 0; i < length; i++) {
            window[i] = 0.5 * (1.0 - Math.cos(2.0 * Math.PI * i / (length - 1)));
        }
        return window;
    }
    
    /**
     * 复数类，用于FFT计算
     */
    public static class Complex {
        public double real;
        public double imag;
        
        public Complex(double real, double imag) {
            this.real = real;
            this.imag = imag;
        }
        
        public Complex add(Complex other) {
            return new Complex(this.real + other.real, this.imag + other.imag);
        }
        
        public Complex subtract(Complex other) {
            return new Complex(this.real - other.real, this.imag - other.imag);
        }
        
        public Complex multiply(Complex other) {
            return new Complex(
                this.real * other.real - this.imag * other.imag,
                this.real * other.imag + this.imag * other.real
            );
        }
        
        public double magnitude() {
            return Math.sqrt(real * real + imag * imag);
        }
    }
    
    /**
     * FFT变换实现（Cooley-Tukey算法）
     * 
     * @param x 输入复数数组
     * @return FFT结果
     */
    public Complex[] fft(Complex[] x) {
        int n = x.length;
        
        // 基本情况
        if (n <= 1) {
            return x.clone();
        }
        
        // 分治
        Complex[] even = new Complex[n / 2];
        Complex[] odd = new Complex[n / 2];
        
        for (int i = 0; i < n / 2; i++) {
            even[i] = x[2 * i];
            odd[i] = x[2 * i + 1];
        }
        
        Complex[] evenResult = fft(even);
        Complex[] oddResult = fft(odd);
        
        Complex[] result = new Complex[n];
        for (int k = 0; k < n / 2; k++) {
            double angle = -2.0 * Math.PI * k / n;
            Complex w = new Complex(Math.cos(angle), Math.sin(angle));
            Complex t = w.multiply(oddResult[k]);
            
            result[k] = evenResult[k].add(t);
            result[k + n / 2] = evenResult[k].subtract(t);
        }
        
        return result;
    }
    
    /**
     * 短时傅里叶变换
     * 对应Python的mystft函数
     * 
     * @param x 输入信号
     * @return 特征向量
     */
    public double[] mystft(double[] x) {
        int winSize = TrainingConfiguration.WINDOW_LENGTH;
        int averageWidth = TrainingConfiguration.AVERAGE_WIDTH;
        int featureLength = TrainingConfiguration.FEATURE_LENGTH;
        
        // 生成汉宁窗
        double[] window = hanningWindow(winSize);
        
        // 对输入数据应用窗函数
        double[] windowed = new double[winSize];
        for (int i = 0; i < winSize && i < x.length; i++) {
            windowed[i] = x[i] * window[i];
        }
        
        // 准备FFT输入
        Complex[] fftInput = new Complex[winSize];
        for (int i = 0; i < winSize; i++) {
            fftInput[i] = new Complex(windowed[i], 0.0);
        }
        
        // 进行FFT
        Complex[] fftResult = fft(fftInput);
        
        // 计算幅度的对数
        double[] magnitudes = new double[winSize / 2];
        for (int i = 0; i < winSize / 2; i++) {
            double magnitude = fftResult[i].magnitude();
            magnitudes[i] = Math.log10(magnitude + 1e-10); // 添加小值避免log(0)
        }
        
        // 归一化幅度
        double[] normalized = new double[winSize / 2];
        for (int i = 0; i < winSize / 2; i++) {
            normalized[i] = Math.abs(magnitudes[i]) / winSize;
        }
        
        // 将中间元素乘以2（除了第一个和最后一个）
        if (normalized.length > 2) {
            for (int i = 1; i < normalized.length - 1; i++) {
                normalized[i] *= 2.0;
            }
        }
        
        // 取前512个点
        int fftOutputSize = Math.min(TrainingConfiguration.FFT_OUTPUT_SIZE, normalized.length);
        double[] fftOutput = Arrays.copyOf(normalized, fftOutputSize);
        
        // 计算可以被整除的长度并重新排列求平均值
        int truncatedLength = (fftOutput.length / averageWidth) * averageWidth;
        double[] averages = new double[truncatedLength / averageWidth];
        
        for (int i = 0; i < averages.length; i++) {
            double sum = 0.0;
            for (int j = 0; j < averageWidth; j++) {
                sum += fftOutput[i * averageWidth + j];
            }
            averages[i] = sum / averageWidth;
        }
        
        // 创建结果数组
        double[] result = new double[featureLength];
        for (int i = 0; i < featureLength && i < averages.length; i++) {
            result[i] = averages[i];
        }
        
        // 归一化处理
        if (result.length > 0) {
            double minValue = Arrays.stream(result).min().orElse(0.0);
            for (int i = 0; i < result.length; i++) {
                result[i] -= minValue;
            }
        }
        
        return result;
    }
}
