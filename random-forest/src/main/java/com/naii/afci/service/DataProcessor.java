package com.naii.afci.service;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Stream;

/**
 * 数据预处理类
 * 实现与Python版本相同的数据处理功能
 */
public class DataProcessor {
    
    /**
     * 递归扫描目录下的所有文件
     * 对应Python的scan_dir函数
     * 
     * @param rootDir 根目录路径
     * @return 文件路径列表
     */
    public List<String> scanDir(String rootDir) {
        List<String> files = new ArrayList<>();
        
        try {
            Path rootPath = Paths.get(rootDir);
            if (!Files.exists(rootPath)) {
                throw new IllegalArgumentException("目录不存在: " + rootDir);
            }
            
            try (Stream<Path> paths = Files.walk(rootPath)) {
                paths.filter(Files::isRegularFile)
                     .filter(path -> !path.getFileName().toString().startsWith("."))
                     .forEach(path -> files.add(path.toString()));
            }
        } catch (IOException e) {
            throw new RuntimeException("扫描目录失败: " + rootDir, e);
        }
        
        return files;
    }
    
    /**
     * 健壮的CSV文件加载函数
     * 对应Python的load_csv_robust函数
     * 
     * @param filePath CSV文件路径
     * @return 数值数组
     */
    public double[] loadCsvRobust(String filePath) {
        List<Double> data = new ArrayList<>();
        
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 分割行并清理每个值
                String[] values = line.split(",");
                for (String val : values) {
                    val = val.trim().replace("\"", "").replace("'", "");
                    if (!val.isEmpty()) {
                        try {
                            data.add(Double.parseDouble(val));
                        } catch (NumberFormatException e) {
                            // 跳过无法转换为数字的值
                            continue;
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("无法加载文件: " + filePath, e);
        }
        
        return data.stream().mapToDouble(Double::doubleValue).toArray();
    }
    
    /**
     * 计算文件可以分成多少个指定长度的段
     * 
     * @param data 数据数组
     * @param windowLength 窗口长度
     * @return 段数
     */
    public int calculateSegments(double[] data, int windowLength) {
        if (data.length < windowLength) {
            return 0;
        }
        
        int fullSegments = data.length / windowLength;
        int remainder = data.length % windowLength;
        
        // 如果有剩余数据，增加一段（向前补齐）
        return remainder > 0 ? fullSegments + 1 : fullSegments;
    }
    
    /**
     * 从数据中提取指定段的数据
     * 
     * @param data 原始数据
     * @param segmentIndex 段索引
     * @param windowLength 窗口长度
     * @return 段数据
     */
    public double[] extractSegment(double[] data, int segmentIndex, int windowLength) {
        int totalSegments = calculateSegments(data, windowLength);
        
        if (segmentIndex >= totalSegments) {
            throw new IllegalArgumentException("段索引超出范围");
        }
        
        if (segmentIndex < data.length / windowLength) {
            // 完整段：正常分割
            int startIdx = segmentIndex * windowLength;
            return Arrays.copyOfRange(data, startIdx, startIdx + windowLength);
        } else {
            // 最后一段：向前补齐，从数据末尾向前取windowLength长度的数据
            int startIdx = data.length - windowLength;
            return Arrays.copyOfRange(data, startIdx, data.length);
        }
    }
}
