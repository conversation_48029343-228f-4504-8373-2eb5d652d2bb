package com.naii.afci.service;

/**
 * 训练配置类，包含所有训练相关的参数
 * 与Python版本的参数保持一致
 */
public class TrainingConfiguration {
    
    // 采样参数
    public static final double SAMPLING_PERIOD = 4e-6;  // 采样周期 T
    public static final double SAMPLING_FREQUENCY = 1.0 / SAMPLING_PERIOD;  // 采样频率 Fs = 250000
    public static final int WINDOW_LENGTH = 1024;  // 窗函数长度 wlen
    public static final int HOP_LENGTH = 1024;  // 重叠长度 hop
    
    // FFT参数
    public static final int FFT_OUTPUT_SIZE = 512;  // FFT输出前512个点
    
    // 特征参数
    public static final int AVERAGE_WIDTH = 5;  // 平均窗口宽度
    public static final int FEATURE_LENGTH = FFT_OUTPUT_SIZE / AVERAGE_WIDTH;  // 特征长度 = 102
    
    // 随机森林参数
    public static final int N_ESTIMATORS = 100;  // 树的数量
    public static final int MIN_SAMPLES_LEAF = 5;  // 叶节点最小样本数
    public static final int RANDOM_STATE = 42;  // 随机种子
    
    // 数据分割参数
    public static final double TEST_SIZE = 0.2;  // 测试集比例
    
    // 标签定义
    public static final int FAULT_LABEL = 1;  // 故障标签
    public static final int NORMAL_LABEL = 0;  // 正常标签
    
    // 私有构造函数，防止实例化
    private TrainingConfiguration() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
