package com.naii.afci.service;

/**
 * 模型评估类
 * 实现与Python版本相同的模型评估指标计算
 */
public class ModelEvaluator {
    
    /**
     * 评估结果类
     */
    public static class EvaluationResult {
        public final int truePositive;
        public final int falsePositive;
        public final int trueNegative;
        public final int falseNegative;
        public final double accuracy;
        public final double precision;
        public final double recall;
        public final double f1Score;
        public final double falsePositiveRate;
        public final double falseNegativeRate;
        public final int totalSamples;
        public final int faultSamples;
        public final int normalSamples;
        
        public EvaluationResult(int tp, int fp, int tn, int fn, double accuracy, 
                              double precision, double recall, double f1Score,
                              double fpr, double fnr, int total, int fault, int normal) {
            this.truePositive = tp;
            this.falsePositive = fp;
            this.trueNegative = tn;
            this.falseNegative = fn;
            this.accuracy = accuracy;
            this.precision = precision;
            this.recall = recall;
            this.f1Score = f1Score;
            this.falsePositiveRate = fpr;
            this.falseNegativeRate = fnr;
            this.totalSamples = total;
            this.faultSamples = fault;
            this.normalSamples = normal;
        }
    }
    
    /**
     * 计算模型评估指标
     * 对应Python脚本中的评估计算部分
     * 
     * @param predictions 预测结果
     * @param trueLabels 真实标签
     * @return 评估结果
     */
    public EvaluationResult evaluate(int[] predictions, int[] trueLabels) {
        if (predictions.length != trueLabels.length) {
            throw new IllegalArgumentException("预测结果和真实标签长度不匹配");
        }
        
        int totalSamples = predictions.length;
        
        // 计算混淆矩阵
        int tp = 0; // 真阳性：预测为故障，实际为故障
        int fp = 0; // 假阳性：预测为故障，实际为正常
        int tn = 0; // 真阴性：预测为正常，实际为正常
        int fn = 0; // 假阴性：预测为正常，实际为故障
        
        int faultSamples = 0;
        int normalSamples = 0;
        
        for (int i = 0; i < totalSamples; i++) {
            int predicted = predictions[i];
            int actual = trueLabels[i];
            
            // 统计实际样本分布
            if (actual == TrainingConfiguration.FAULT_LABEL) {
                faultSamples++;
            } else {
                normalSamples++;
            }
            
            // 计算混淆矩阵元素
            if (predicted == TrainingConfiguration.FAULT_LABEL && actual == TrainingConfiguration.FAULT_LABEL) {
                tp++;
            } else if (predicted == TrainingConfiguration.FAULT_LABEL && actual == TrainingConfiguration.NORMAL_LABEL) {
                fp++;
            } else if (predicted == TrainingConfiguration.NORMAL_LABEL && actual == TrainingConfiguration.NORMAL_LABEL) {
                tn++;
            } else if (predicted == TrainingConfiguration.NORMAL_LABEL && actual == TrainingConfiguration.FAULT_LABEL) {
                fn++;
            }
        }
        
        // 计算评估指标
        double accuracy = (double) (tp + tn) / totalSamples;
        double precision = (tp + fp) > 0 ? (double) tp / (tp + fp) : 0.0;
        double recall = (tp + fn) > 0 ? (double) tp / (tp + fn) : 0.0;
        double f1Score = (precision + recall) > 0 ? 2.0 * (precision * recall) / (precision + recall) : 0.0;
        double falsePositiveRate = (fp + tn) > 0 ? (double) fp / (fp + tn) : 0.0;
        double falseNegativeRate = (fn + tp) > 0 ? (double) fn / (fn + tp) : 0.0;
        
        return new EvaluationResult(tp, fp, tn, fn, accuracy, precision, recall, 
                                  f1Score, falsePositiveRate, falseNegativeRate,
                                  totalSamples, faultSamples, normalSamples);
    }
    
    /**
     * 打印详细的评估结果
     * 对应Python脚本中的结果输出部分
     * 
     * @param result 评估结果
     * @param datasetName 数据集名称（如"训练集"、"测试集"）
     */
    public void printEvaluationResult(EvaluationResult result, String datasetName) {
        System.out.println("\n" + datasetName + "评估结果:");
        System.out.println("=".repeat(50));
        System.out.println("数据分布:");
        System.out.printf("  %s总样本数: %d%n", datasetName, result.totalSamples);
        System.out.printf("  %s故障样本数: %d%n", datasetName, result.faultSamples);
        System.out.printf("  %s正常样本数: %d%n", datasetName, result.normalSamples);
        
        System.out.println("\n混淆矩阵:");
        System.out.printf("  真阳性(TP): %d  假阳性(FP): %d%n", result.truePositive, result.falsePositive);
        System.out.printf("  假阴性(FN): %d  真阴性(TN): %d%n", result.falseNegative, result.trueNegative);
        
        System.out.println("\n性能指标:");
        System.out.printf("  准确率(Accuracy): %.4f (%.2f%%)%n", result.accuracy, result.accuracy * 100);
        System.out.printf("  精确率(Precision): %.4f (%.2f%%)%n", result.precision, result.precision * 100);
        System.out.printf("  召回率(Recall): %.4f (%.2f%%)%n", result.recall, result.recall * 100);
        System.out.printf("  F1分数: %.4f%n", result.f1Score);
        System.out.printf("  误报率(FPR): %.4f (%.2f%%)%n", result.falsePositiveRate, result.falsePositiveRate * 100);
        System.out.printf("  漏报率(FNR): %.4f (%.2f%%)%n", result.falseNegativeRate, result.falseNegativeRate * 100);
    }
    
    /**
     * 计算准确率
     * 
     * @param predictions 预测结果
     * @param trueLabels 真实标签
     * @return 准确率
     */
    public double calculateAccuracy(int[] predictions, int[] trueLabels) {
        if (predictions.length != trueLabels.length) {
            throw new IllegalArgumentException("预测结果和真实标签长度不匹配");
        }
        
        int correct = 0;
        for (int i = 0; i < predictions.length; i++) {
            if (predictions[i] == trueLabels[i]) {
                correct++;
            }
        }
        
        return (double) correct / predictions.length;
    }
}
