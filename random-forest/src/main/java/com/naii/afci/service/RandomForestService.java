package com.naii.afci.service;

import smile.classification.RandomForest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 随机森林训练服务
 * 实现与Python版本完全一致的随机森林训练流程
 */
public class RandomForestService {

    private final DataProcessor dataProcessor;
    private final FFTFeatureExtractor fftExtractor;
    private final RandomForestTrainer trainer;
    private final ModelEvaluator evaluator;

    public RandomForestService() {
        this.dataProcessor = new DataProcessor();
        this.fftExtractor = new FFTFeatureExtractor();
        this.trainer = new RandomForestTrainer();
        this.evaluator = new ModelEvaluator();
    }

    /**
     * 数据预处理主函数
     * 对应Python的data_pre_process函数
     *
     * @param arcPath 故障电流数据路径
     * @param normalPath 正常电流数据路径
     * @return 处理结果 [特征矩阵, 标签数组, 故障样本数, 正常样本数]
     */
    public Object[] dataPreProcess(String arcPath, String normalPath) {
        System.out.println("正在处理所有数据...");

        // 获取文件列表
        List<String> arcFiles = dataProcessor.scanDir(arcPath);
        List<String> normalFiles = dataProcessor.scanDir(normalPath);

        if (arcFiles.isEmpty() || normalFiles.isEmpty()) {
            throw new RuntimeException("未找到数据文件，请检查路径");
        }

        System.out.println("找到故障文件: " + arcFiles.size() + " 个");
        System.out.println("找到正常文件: " + normalFiles.size() + " 个");

        // 处理故障数据
        System.out.println("\n正在处理故障数据文件...");
        ProcessResult arcResult = processFiles(arcFiles, "故障");

        // 处理正常数据
        System.out.println("\n正在处理正常数据文件...");
        ProcessResult normalResult = processFiles(normalFiles, "正常");

        // 合并特征矩阵
        int totalSamples = arcResult.samples + normalResult.samples;
        double[][] allFeatures = new double[totalSamples][];
        int[] allLabels = new int[totalSamples];

        // 复制故障数据
        System.arraycopy(arcResult.features, 0, allFeatures, 0, arcResult.samples);
        for (int i = 0; i < arcResult.samples; i++) {
            allLabels[i] = TrainingConfiguration.FAULT_LABEL;
        }

        // 复制正常数据
        System.arraycopy(normalResult.features, 0, allFeatures, arcResult.samples, normalResult.samples);
        for (int i = arcResult.samples; i < totalSamples; i++) {
            allLabels[i] = TrainingConfiguration.NORMAL_LABEL;
        }

        System.out.printf("\n数据处理完成:%n");
        System.out.printf("  总样本数: %d%n", totalSamples);
        System.out.printf("  故障样本数: %d%n", arcResult.samples);
        System.out.printf("  正常样本数: %d%n", normalResult.samples);
        System.out.printf("  特征维度: %d%n", TrainingConfiguration.FEATURE_LENGTH);

        return new Object[]{allFeatures, allLabels, arcResult.samples, normalResult.samples};
    }

    /**
     * 处理结果类
     */
    private static class ProcessResult {
        final double[][] features;
        final int samples;

        ProcessResult(double[][] features, int samples) {
            this.features = features;
            this.samples = samples;
        }
    }

    /**
     * 处理文件列表，提取特征
     *
     * @param files 文件路径列表
     * @param dataType 数据类型（用于日志输出）
     * @return 处理结果
     */
    private ProcessResult processFiles(List<String> files, String dataType) {
        List<double[]> allFeatures = new ArrayList<>();

        // 第一步：计算每个文件的分段数
        System.out.println("计算" + dataType + "数据分段数...");
        List<Integer> segmentCounts = new ArrayList<>();
        int totalSegments = 0;

        for (String filePath : files) {
            try {
                double[] data = dataProcessor.loadCsvRobust(filePath);
                int segments = dataProcessor.calculateSegments(data, TrainingConfiguration.WINDOW_LENGTH);
                segmentCounts.add(segments);
                totalSegments += segments;
            } catch (Exception e) {
                System.err.println("处理文件失败: " + filePath + ", 错误: " + e.getMessage());
                segmentCounts.add(0);
            }
        }

        System.out.println(dataType + "数据总分段数: " + totalSegments);

        // 第二步：处理每个文件的每个分段
        System.out.println("处理" + dataType + "数据特征提取...");
        int processedSegments = 0;

        for (int fileIndex = 0; fileIndex < files.size(); fileIndex++) {
            String filePath = files.get(fileIndex);
            int segments = segmentCounts.get(fileIndex);

            if (segments == 0) {
                continue;
            }

            try {
                double[] data = dataProcessor.loadCsvRobust(filePath);

                for (int segmentIndex = 0; segmentIndex < segments; segmentIndex++) {
                    // 提取分段数据
                    double[] segmentData = dataProcessor.extractSegment(data, segmentIndex, TrainingConfiguration.WINDOW_LENGTH);

                    // 计算FFT特征
                    double[] features = fftExtractor.mystft(segmentData);
                    allFeatures.add(features);

                    processedSegments++;
                    if (processedSegments % 100 == 0) {
                        System.out.printf("已处理 %d/%d 个%s数据分段%n", processedSegments, totalSegments, dataType);
                    }
                }
            } catch (Exception e) {
                System.err.println("处理文件失败: " + filePath + ", 错误: " + e.getMessage());
            }
        }

        System.out.println(dataType + "数据特征提取完成，共处理 " + processedSegments + " 个分段");

        // 转换为二维数组
        double[][] features = allFeatures.toArray(new double[0][]);
        return new ProcessResult(features, processedSegments);
    }

    /**
     * 完整的训练流程
     * 对应Python脚本的main函数
     *
     * @param arcPath 故障数据路径
     * @param normalPath 正常数据路径
     * @return 训练好的模型
     */
    public RandomForest trainModel(String arcPath, String normalPath) {
        // 显示开始时间
        LocalDateTime startTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        System.out.println(startTime.format(formatter) + " 开始执行");

        // 数据预处理
        Object[] preprocessResult = dataPreProcess(arcPath, normalPath);
        double[][] allData = (double[][]) preprocessResult[0];
        int[] allLabels = (int[]) preprocessResult[1];
        int arcLen = (int) preprocessResult[2];
        int normLen = (int) preprocessResult[3];

        // 数据分割
        System.out.println("\n随机分割数据为训练集(80%)和测试集(20%)...");
        Object[] splitResult = trainer.trainTestSplit(allData, allLabels,
                                                    TrainingConfiguration.TEST_SIZE,
                                                    TrainingConfiguration.RANDOM_STATE);

        double[][] trainData = (double[][]) splitResult[0];
        double[][] testData = (double[][]) splitResult[1];
        int[] trainLabels = (int[]) splitResult[2];
        int[] testLabels = (int[]) splitResult[3];

        printDataSplitInfo(allData, trainData, testData, trainLabels, testLabels);

        // 训练模型
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);

        // 评估模型
        evaluateModel(model, trainData, trainLabels, testData, testLabels);

        // 显示完成时间
        LocalDateTime endTime = LocalDateTime.now();
        System.out.println(endTime.format(formatter) + " 模型训练已完成");

        return model;
    }

    /**
     * 打印数据分割信息
     */
    private void printDataSplitInfo(double[][] allData, double[][] trainData, double[][] testData,
                                   int[] trainLabels, int[] testLabels) {
        System.out.printf("\n数据分割完成:%n");
        System.out.printf("  训练集样本数: %d (%.1f%%)%n", trainData.length,
                         (double) trainData.length / allData.length * 100);
        System.out.printf("  测试集样本数: %d (%.1f%%)%n", testData.length,
                         (double) testData.length / allData.length * 100);

        // 统计训练集中的标签分布
        int trainFaultCount = 0, trainNormalCount = 0;
        for (int label : trainLabels) {
            if (label == TrainingConfiguration.FAULT_LABEL) {
                trainFaultCount++;
            } else {
                trainNormalCount++;
            }
        }

        // 统计测试集中的标签分布
        int testFaultCount = 0, testNormalCount = 0;
        for (int label : testLabels) {
            if (label == TrainingConfiguration.FAULT_LABEL) {
                testFaultCount++;
            } else {
                testNormalCount++;
            }
        }

        System.out.printf("  训练集中故障样本数: %d%n", trainFaultCount);
        System.out.printf("  训练集中正常样本数: %d%n", trainNormalCount);
        System.out.printf("  测试集中故障样本数: %d%n", testFaultCount);
        System.out.printf("  测试集中正常样本数: %d%n", testNormalCount);
    }

    /**
     * 评估模型性能
     */
    private void evaluateModel(RandomForest model, double[][] trainData, int[] trainLabels,
                              double[][] testData, int[] testLabels) {
        // 在训练集上预测
        int[] trainPred = trainer.predict(model, trainData);
        double trainAcc = evaluator.calculateAccuracy(trainPred, trainLabels);
        System.out.printf("训练集准确率: %.4f%n", trainAcc);

        // 在测试集上预测
        int[] testPred = trainer.predict(model, testData);

        // 详细评估
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(testPred, testLabels);
        evaluator.printEvaluationResult(result, "测试集");
    }
}

