package com.naii.afci.service;

import smile.classification.RandomForest;
import smile.base.cart.SplitRule;
import smile.data.DataFrame;
import smile.data.formula.Formula;
import smile.data.vector.DoubleVector;
import smile.data.vector.IntVector;

import java.util.Random;

/**
 * 随机森林训练类
 * 使用Smile库实现与Python版本相同的随机森林训练功能
 */
public class RandomForestTrainer {

    /**
     * 训练随机森林模型
     * 对应Python的train_random_forest函数
     *
     * @param trainData 训练数据
     * @param trainLabels 训练标签
     * @return 训练好的随机森林模型
     */
    public RandomForest trainRandomForest(double[][] trainData, int[] trainLabels) {
        System.out.println("\n开始训练随机森林模型...");

        // 根据RandomForest源码，需要使用Formula和DataFrame
        // 创建DataFrame
        DataFrame dataFrame = createDataFrame(trainData, trainLabels);

        // 创建Formula - 使用所有特征预测标签
        Formula formula = Formula.lhs("label");

        // 使用正确的API调用
        RandomForest model = RandomForest.fit(
            formula,
            dataFrame,
            TrainingConfiguration.N_ESTIMATORS,     // ntrees = 100
            0,                                      // mtry = 0 (使用默认值sqrt(p))
            SplitRule.GINI,                         // 分割规则
            -1,                                     // maxDepth = -1 (无限制)
            dataFrame.size() / 5,                   // maxNodes
            TrainingConfiguration.MIN_SAMPLES_LEAF, // nodeSize = 5
            1.0                                     // subsample = 1.0
        );

        System.out.println("随机森林模型训练完成");
        return model;
    }



    /**
     * 创建DataFrame用于Smile训练
     *
     * @param data 特征数据
     * @param labels 标签数据
     * @return DataFrame对象
     */
    private DataFrame createDataFrame(double[][] data, int[] labels) {
        int numSamples = data.length;
        int numFeatures = data[0].length;

        try {
            // 方法1: 尝试使用简单的DataFrame创建方式
            // 合并特征数据和标签数据
            double[][] combinedData = new double[numSamples][numFeatures + 1];
            for (int i = 0; i < numSamples; i++) {
                System.arraycopy(data[i], 0, combinedData[i], 0, numFeatures);
                combinedData[i][numFeatures] = labels[i]; // 标签作为最后一列
            }

            // 创建列名
            String[] columnNames = new String[numFeatures + 1];
            for (int i = 0; i < numFeatures; i++) {
                columnNames[i] = "feature_" + i;
            }
            columnNames[numFeatures] = "label";

            // 尝试使用DataFrame.of方法
            return DataFrame.of(combinedData, columnNames);

        } catch (Exception e1) {
            return null;
        }
    }
    /**
     * 使用模型进行预测
     *
     * @param model 训练好的模型
     * @param testData 测试数据
     * @return 预测结果
     */
    public int[] predict(RandomForest model, double[][] testData) {
        int[] predictions = new int[testData.length];

        for (int i = 0; i < testData.length; i++) {
            predictions[i] = model.predict(testData[i]);
        }

        return predictions;
    }

    /**
     * 获取模型的袋外评分（如果可用）
     *
     * @param model 随机森林模型
     * @return 袋外评分，如果不可用则返回-1
     */
    public double getOOBScore(RandomForest model) {
        try {
            // Smile的RandomForest可能不直接提供OOB score
            // 这里返回-1表示不可用
            return -1.0;
        } catch (Exception e) {
            return -1.0;
        }
    }

    /**
     * 数据分割：将数据随机分为训练集和测试集
     * 对应Python的train_test_split函数
     *
     * @param data 原始数据
     * @param labels 原始标签
     * @param testSize 测试集比例
     * @param randomState 随机种子
     * @return 分割后的数据 [trainData, testData, trainLabels, testLabels]
     */
    public Object[] trainTestSplit(double[][] data, int[] labels, double testSize, int randomState) {
        Random random = new Random(randomState);
        int totalSamples = data.length;
        int testSamples = (int) (totalSamples * testSize);
        int trainSamples = totalSamples - testSamples;

        // 创建索引数组并打乱
        Integer[] indices = new Integer[totalSamples];
        for (int i = 0; i < totalSamples; i++) {
            indices[i] = i;
        }

        // 使用Fisher-Yates洗牌算法
        for (int i = totalSamples - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            Integer temp = indices[i];
            indices[i] = indices[j];
            indices[j] = temp;
        }

        // 分配数据
        double[][] trainData = new double[trainSamples][];
        double[][] testData = new double[testSamples][];
        int[] trainLabels = new int[trainSamples];
        int[] testLabels = new int[testSamples];

        // 训练集
        for (int i = 0; i < trainSamples; i++) {
            int idx = indices[i];
            trainData[i] = data[idx].clone();
            trainLabels[i] = labels[idx];
        }

        // 测试集
        for (int i = 0; i < testSamples; i++) {
            int idx = indices[trainSamples + i];
            testData[i] = data[idx].clone();
            testLabels[i] = labels[idx];
        }

        return new Object[]{trainData, testData, trainLabels, testLabels};
    }
}
