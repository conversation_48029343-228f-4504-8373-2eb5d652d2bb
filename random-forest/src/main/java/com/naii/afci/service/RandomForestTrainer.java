package com.naii.afci.service;

import smile.classification.RandomForest;
import smile.data.DataFrame;
import smile.data.formula.Formula;
import smile.data.type.DataTypes;
import smile.data.type.StructType;
import smile.data.vector.DoubleVector;
import smile.data.vector.IntVector;

import java.util.Arrays;
import java.util.Random;

/**
 * 随机森林训练类
 * 使用Smile库实现与Python版本相同的随机森林训练功能
 */
public class RandomForestTrainer {
    
    /**
     * 训练随机森林模型
     * 对应Python的train_random_forest函数
     * 
     * @param trainData 训练数据
     * @param trainLabels 训练标签
     * @return 训练好的随机森林模型
     */
    public RandomForest trainRandomForest(double[][] trainData, int[] trainLabels) {
        System.out.println("\n开始训练随机森林模型...");
        
        // 创建DataFrame
        DataFrame dataFrame = createDataFrame(trainData, trainLabels);
        
        // 创建公式（所有特征用于预测标签）
        Formula formula = Formula.lhs("label");
        
        // 设置随机森林参数，与Python版本保持一致
        RandomForest model = RandomForest.fit(
            formula,
            dataFrame,
            TrainingConfiguration.N_ESTIMATORS,     // n_estimators = 100
            -1,                                     // mtry = -1 (使用默认值)
            -1,                                     // maxDepth = -1 (无限制)
            TrainingConfiguration.MIN_SAMPLES_LEAF, // nodeSize = 5 (min_samples_leaf)
            1.0,                                    // subsample = 1.0
            TrainingConfiguration.RANDOM_STATE      // random seed = 42
        );
        
        System.out.println("随机森林模型训练完成");
        return model;
    }
    
    /**
     * 创建DataFrame用于Smile训练
     * 
     * @param data 特征数据
     * @param labels 标签数据
     * @return DataFrame对象
     */
    private DataFrame createDataFrame(double[][] data, int[] labels) {
        int numSamples = data.length;
        int numFeatures = data[0].length;
        
        // 创建结构类型
        StructType.Field[] fields = new StructType.Field[numFeatures + 1];
        
        // 添加特征字段
        for (int i = 0; i < numFeatures; i++) {
            fields[i] = new StructType.Field("feature_" + i, DataTypes.DoubleType);
        }
        
        // 添加标签字段
        fields[numFeatures] = new StructType.Field("label", DataTypes.IntegerType);
        
        StructType schema = new StructType(fields);
        
        // 创建数据向量
        DoubleVector[] featureVectors = new DoubleVector[numFeatures];
        for (int i = 0; i < numFeatures; i++) {
            double[] featureColumn = new double[numSamples];
            for (int j = 0; j < numSamples; j++) {
                featureColumn[j] = data[j][i];
            }
            featureVectors[i] = DoubleVector.of(fields[i], featureColumn);
        }
        
        IntVector labelVector = IntVector.of(fields[numFeatures], labels);
        
        // 组合所有向量
        smile.data.vector.Vector[] allVectors = new smile.data.vector.Vector[numFeatures + 1];
        System.arraycopy(featureVectors, 0, allVectors, 0, numFeatures);
        allVectors[numFeatures] = labelVector;
        
        return DataFrame.of(allVectors);
    }
    
    /**
     * 使用模型进行预测
     * 
     * @param model 训练好的模型
     * @param testData 测试数据
     * @return 预测结果
     */
    public int[] predict(RandomForest model, double[][] testData) {
        int[] predictions = new int[testData.length];
        
        for (int i = 0; i < testData.length; i++) {
            predictions[i] = model.predict(testData[i]);
        }
        
        return predictions;
    }
    
    /**
     * 获取模型的袋外评分（如果可用）
     * 
     * @param model 随机森林模型
     * @return 袋外评分，如果不可用则返回-1
     */
    public double getOOBScore(RandomForest model) {
        try {
            // Smile的RandomForest可能不直接提供OOB score
            // 这里返回-1表示不可用
            return -1.0;
        } catch (Exception e) {
            return -1.0;
        }
    }
    
    /**
     * 数据分割：将数据随机分为训练集和测试集
     * 对应Python的train_test_split函数
     * 
     * @param data 原始数据
     * @param labels 原始标签
     * @param testSize 测试集比例
     * @param randomState 随机种子
     * @return 分割后的数据 [trainData, testData, trainLabels, testLabels]
     */
    public Object[] trainTestSplit(double[][] data, int[] labels, double testSize, int randomState) {
        Random random = new Random(randomState);
        int totalSamples = data.length;
        int testSamples = (int) (totalSamples * testSize);
        int trainSamples = totalSamples - testSamples;
        
        // 创建索引数组并打乱
        Integer[] indices = new Integer[totalSamples];
        for (int i = 0; i < totalSamples; i++) {
            indices[i] = i;
        }
        
        // 使用Fisher-Yates洗牌算法
        for (int i = totalSamples - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            Integer temp = indices[i];
            indices[i] = indices[j];
            indices[j] = temp;
        }
        
        // 分配数据
        double[][] trainData = new double[trainSamples][];
        double[][] testData = new double[testSamples][];
        int[] trainLabels = new int[trainSamples];
        int[] testLabels = new int[testSamples];
        
        // 训练集
        for (int i = 0; i < trainSamples; i++) {
            int idx = indices[i];
            trainData[i] = data[idx].clone();
            trainLabels[i] = labels[idx];
        }
        
        // 测试集
        for (int i = 0; i < testSamples; i++) {
            int idx = indices[trainSamples + i];
            testData[i] = data[idx].clone();
            testLabels[i] = labels[idx];
        }
        
        return new Object[]{trainData, testData, trainLabels, testLabels};
    }
}
