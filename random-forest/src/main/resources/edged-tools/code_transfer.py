#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
把 sklearn RandomForestClassifier 转换成 C 代码
运行示例：
    python sklearn2c.py --model clf.joblib --output rf_model.c
"""
import argparse
import joblib
import re
import numpy as np

def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument('--model',  help='joblib 保存的 RandomForestClassifier', default='random_forest_model.pkl')
    p.add_argument('--output', default='rf_model.c', help='输出的 C 源文件')
    return p.parse_args()

def sanitize(name: str) -> str:
    """把变量名中的非法字符去掉"""
    return re.sub(r'[^0-9a-zA-Z_]', '_', str(name))

def write_header(fp, n_classes):
    fp.write('#include <stdio.h>\n')
    fp.write('#include <stdint.h>\n\n')
    fp.write('/*  单棵树推理函数声明  */\n')
    fp.write('float predictTree(float features[],\n'
             '                  const float cutPoint[],\n'
             '                  const uint8_t cutPredictor[],\n'
             '                  const uint16_t classProba[][{}],\n'
             '                  const short children[][2],\n'
             '                  int node);\n\n'.format(n_classes))
    fp.write('/*  森林主接口  */\n')
    fp.write('int predictForest(float features[]);\n\n')

def export_tree(fp, idx, tree, n_classes):
    """把一棵树写成 4 个数组 + 1 个函数"""
    t = tree.tree_
    n_nodes = t.node_count

    # 1) cutPoint
    fp.write('const float cutPoint{}[] = {{\n   '.format(idx))
    thresholds = [t.threshold[i] if t.children_left[i] != t.children_right[i] else 100.0
                  for i in range(n_nodes)]
    fp.write(', '.join('{:.6f}'.format(v) for v in thresholds))
    fp.write('\n};\n')

    # 2) cutPredictor
    fp.write('const uint8_t cutPredictor{}[] = {{\n   '.format(idx))
    predictors = [int(t.feature[i]) + 1 if t.children_left[i] != t.children_right[i] else 0
                  for i in range(n_nodes)]
    fp.write(', '.join(str(v) for v in predictors))
    fp.write('\n};\n')

    # 3) classProba
    fp.write('const uint16_t classProba{}[][{}] = {{\n'.format(idx, n_classes))
    for i in range(n_nodes):
        if t.children_left[i] == t.children_right[i]:
            # 叶节点，概率 ×10000
            proba = t.value[i][0]
            proba = (proba / proba.sum()) * 10000
            proba = np.round(proba).astype(int)
        else:
            proba = [0] * n_classes
        fp.write('   {' + ', '.join(str(p) for p in proba) + '}')
        fp.write(',' if i < n_nodes-1 else '')
    fp.write('};\n')

    # 4) children
    fp.write('const short children{}[][2] = {{\n'.format(idx))
    for i in range(n_nodes):
        l = t.children_left[i] + 1   # 1-based
        r = t.children_right[i] + 1
        fp.write('   {{{}, {}}}'.format(l, r))
        fp.write(',' if i < n_nodes-1 else '')
    fp.write('};\n\n')

def write_forest(fp, clf):
    fp.write('/*  森林主预测函数  */\n')
    fp.write('int predictForest(float features[]) {\n')
    fp.write('    float result = 0.0f;\n')
    for t in range(clf.n_estimators):
        fp.write('    float c{0} = predictTree(features, cutPoint{0}, cutPredictor{0}, classProba{0}, children{0}, 0);\n'.format(t))
        fp.write('    result = result + (c{0} - result) / {0}.0f;\n'.format(t))
    fp.write('    return result > 0.5f ? 0 : 1;\n}\n\n')

def write_predictTree(fp, n_classes):
    fp.write('float predictTree(float features[],\n'
             '                  const float cutPoint[],\n'
             '                  const uint8_t cutPredictor[],\n'
             '                  const uint16_t classProba[][{}],\n'
             '                  const short children[][2],\n'
             '                  int node) {{\n'.format(n_classes))
    fp.write('    if (cutPoint[node] == 100.0f) {\n')
    fp.write('        /* 叶节点，返回正类概率 */\n')
    fp.write('        return classProba[node][1] / 10000.0f;\n')
    fp.write('    }\n')
    fp.write('    if (features[cutPredictor[node] - 1] < cutPoint[node]) {\n')
    fp.write('        return predictTree(features, cutPoint, cutPredictor, classProba, children, children[node][0] - 1);\n')
    fp.write('    } else {\n')
    fp.write('        return predictTree(features, cutPoint, cutPredictor, classProba, children, children[node][1] - 1);\n')
    fp.write('    }\n}\n')

def main():
    args = parse_args()
    clf = joblib.load(args.model)

    if not hasattr(clf, 'estimators_'):
        raise ValueError("模型必须是 RandomForestClassifier 且已训练")

    n_classes = clf.n_classes_
    if n_classes != 2:
        print("警告：当前只针对二分类做了阈值 0.5 处理，多类需自行扩展")

    with open(args.output, 'w') as fp:
        fp.write('/*  Auto-generated by sklearn2c.py  */\n\n')
        write_header(fp, n_classes)
        for idx, tree in enumerate(clf.estimators_):
            export_tree(fp, idx, tree, n_classes)
        write_forest(fp, clf)
        write_predictTree(fp, n_classes)
    print('已生成 {}'.format(args.output))

if __name__ == '__main__':
    main()