import numpy as np
from sklearn.ensemble import RandomForestClassifier
from scipy import signal
import os
from tqdm import tqdm
import joblib
import pandas as pd

def scan_dir(root_dir):
    """
    递归扫描目录下的所有文件
    :param root_dir: 根目录路径
    :return: 文件路径的numpy数组
    """
    files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            # 跳过.和..目录
            if filename in ('.', '..'):
                continue
            full_path = os.path.join(dirpath, filename)
            files.append(full_path)
    
    # 转换为numpy数组并保持与Matlab相同的格式
    return np.array(files).reshape(1, -1)
import time

def load_csv_robust(file_path):
    """
    健壮的CSV文件加载函数，能够处理包含引号和其他特殊字符的数据
    :param file_path: CSV文件路径
    :return: 数值数组
    """
    try:
        # 首先尝试使用pandas读取CSV文件
        df = pd.read_csv(file_path, header=None, dtype=str)

        # 如果只有一列数据，将其转换为一维数组
        if df.shape[1] == 1:
            data = df.iloc[:, 0].values
        else:
            # 如果有多列，取第一列或者展平所有数据
            data = df.values.flatten()

        # 清理数据：移除引号和其他非数字字符
        cleaned_data = []
        for item in data:
            if pd.isna(item):
                continue
            # 转换为字符串并清理
            item_str = str(item).strip()
            # 移除引号
            item_str = item_str.replace('"', '').replace("'", '')
            # 尝试转换为浮点数
            try:
                cleaned_data.append(float(item_str))
            except ValueError:
                # 如果无法转换为数字，跳过这个值
                continue

        return np.array(cleaned_data)

    except Exception as e:
        # 如果pandas失败，尝试使用numpy的genfromtxt
        try:
            # 使用genfromtxt，它对格式更宽容
            data = np.genfromtxt(file_path, delimiter=',', invalid_raise=False,
                               converters={i: lambda s: float(s.decode().replace('"', '').replace("'", ''))
                                         if s else np.nan for i in range(10)})
            # 移除NaN值
            data = data[~np.isnan(data)]
            return data
        except Exception as e2:
            # 最后尝试逐行读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                cleaned_data = []
                for line in lines:
                    # 分割行并清理每个值
                    values = line.strip().split(',')
                    for val in values:
                        val = val.strip().replace('"', '').replace("'", '')
                        try:
                            cleaned_data.append(float(val))
                        except ValueError:
                            continue

                return np.array(cleaned_data)
            except Exception as e3:
                raise ValueError(f"无法加载文件 {file_path}: {e3}")

def data_pre_process(arc_path, normal_path, average_width, feature_length):
    """
    数据预处理函数，与MATLAB代码功能一致
    :param arc_path: 故障电流数据路径
    :param normal_path: 正常电流数据路径
    :param average_width: 平均窗口宽度
    :param feature_length: 特征长度
    :return: 特征矩阵，标签，故障样本数，正常样本数
    """
    # 采样参数设置
    T = 4e-6  # 采样周期
    Fs = 1/T  # 采样频率
    wlen = 1024  # 窗函数长度
    hop = 1024  # 重叠长度
    
    def mystft(x, win_size=wlen, hop_size=hop, fs=Fs):
        """
        短时傅里叶变换，与MATLAB代码功能一致
        """
        # 使用汉宁窗
        win = signal.windows.hann(win_size, sym=False)
        # 对输入数据进行分段处理
        x_windowed = x * win
        # 进行FFT
        X = np.fft.fft(x_windowed, win_size)
        # 计算幅度的对数
        X1 = np.log10(np.abs(X))
        # 提取一半的频谱
        X2 = X1[:win_size//2]
        # 归一化幅度
        A2 = np.abs(X2)/win_size
        A1 = A2.copy()
        # 将中间元素乘以2
        A1[1:-1] = 2 * A1[1:-1]
        # 取前512个点
        FFT = A1[:512]
        
        # 计算可以被整除的长度
        data_length = len(FFT)
        truncated_length = (data_length // average_width) * average_width
        truncated_data = FFT[:truncated_length]
        
        # 重新排列并求平均值
        reshaped_data = truncated_data.reshape(-1, average_width)
        averages = np.mean(reshaped_data, axis=1)
        
        # 创建结果数组
        result = np.zeros(feature_length)
        result[:feature_length] = averages[:feature_length]
        
        # 归一化处理
        min_value = np.min(result)
        result = result - min_value
        
        return result
    
    # 获取文件列表
    arc_files = scan_dir(arc_path)
    norm_files = scan_dir(normal_path)
    
    if arc_files.size == 0 or norm_files.size == 0:
        raise ValueError('未找到数据文件，请检查路径')
    
    # 处理故障数据
    arc_test_files = arc_files[0]
    arc_test_files_length = len(arc_test_files)
    arc_test_segment = np.zeros(arc_test_files_length)
    
    # 计算每个文件可以分成多少个1024点的段
    print('正在处理故障数据文件...')
    for k in tqdm(range(arc_test_files_length), desc='计算故障数据分段数'):
        try:
            yk = load_csv_robust(arc_test_files[k])
            length_yk = len(yk)
            if length_yk < wlen:
                # 如果数据长度小于窗口长度，跳过该文件
                arc_test_segment[k] = 0
            else:
                # 计算完整段数
                full_segments = length_yk // wlen
                # 检查是否有剩余数据
                remainder = length_yk % wlen
                if remainder > 0:
                    # 有剩余数据，增加一段（向前补齐）
                    arc_test_segment[k] = full_segments + 1
                else:
                    # 无剩余数据，只有完整段
                    arc_test_segment[k] = full_segments
        except Exception as e:
            print(f"Error loading file {arc_test_files[k]}: {e}")
            arc_test_segment[k] = 0
            continue
    
    # 计算总的故障样本数
    arc_test_seg_length = int(np.sum(arc_test_segment))
    arc_test_seg_data = np.zeros((arc_test_seg_length, feature_length))
    
    # 处理每个故障文件
    current_idx = 0
    total_segments = int(np.sum(arc_test_segment))
    with tqdm(total=total_segments, desc='处理故障数据特征提取') as pbar:
        for k in range(arc_test_files_length):
            try:
                yk = load_csv_robust(arc_test_files[k])
            except Exception as e:
                print(f"Error loading file {arc_test_files[k]}: {e}")
                continue
            length_yk = len(yk)
            num_segments = int(arc_test_segment[k])

            for i in range(num_segments):
                if i < length_yk // wlen:
                    # 完整段：正常分割
                    yk_i = yk[i*wlen : (i+1)*wlen]
                else:
                    # 最后一段：向前补齐
                    # 从数据末尾向前取wlen长度的数据
                    start_idx = length_yk - wlen
                    yk_i = yk[start_idx : length_yk]

                # 计算STFT特征
                S2 = mystft(yk_i)
                arc_test_seg_data[current_idx] = S2
                current_idx += 1
                pbar.update(1)
    
    # 处理正常数据
    norm_test_files = norm_files[0]
    norm_test_files_length = len(norm_test_files)
    norm_test_segment = np.zeros(norm_test_files_length)
    
    # 计算每个文件可以分成多少个1024点的段
    print('\n正在处理正常数据文件...')
    for k in tqdm(range(norm_test_files_length), desc='计算正常数据分段数'):
        try:
            yk = load_csv_robust(norm_test_files[k])
            length_yk = len(yk)
            if length_yk < wlen:
                # 如果数据长度小于窗口长度，跳过该文件
                norm_test_segment[k] = 0
            else:
                # 计算完整段数
                full_segments = length_yk // wlen
                # 检查是否有剩余数据
                remainder = length_yk % wlen
                if remainder > 0:
                    # 有剩余数据，增加一段（向前补齐）
                    norm_test_segment[k] = full_segments + 1
                else:
                    # 无剩余数据，只有完整段
                    norm_test_segment[k] = full_segments
        except Exception as e:
            print(f"Error loading file {norm_test_files[k]}: {e}")
            norm_test_segment[k] = 0
            continue
    
    # 计算总的正常样本数
    norm_test_seg_length = int(np.sum(norm_test_segment))
    norm_test_seg_data = np.zeros((norm_test_seg_length, feature_length))
    
    # 处理每个正常文件
    current_idx = 0
    total_segments = int(np.sum(norm_test_segment))
    with tqdm(total=total_segments, desc='处理正常数据特征提取') as pbar:
        for k in range(norm_test_files_length):
            try:
                yk = load_csv_robust(norm_test_files[k])
            except Exception as e:
                print(f"Error loading file {norm_test_files[k]}: {e}")
                continue
            length_yk = len(yk)
            num_segments = int(norm_test_segment[k])

            for i in range(num_segments):
                if i < length_yk // wlen:
                    # 完整段：正常分割
                    yk_i = yk[i*wlen : (i+1)*wlen]
                else:
                    # 最后一段：向前补齐
                    # 从数据末尾向前取wlen长度的数据
                    start_idx = length_yk - wlen
                    yk_i = yk[start_idx : length_yk]

                # 计算STFT特征
                S2 = mystft(yk_i)
                norm_test_seg_data[current_idx] = S2
                current_idx += 1
                pbar.update(1)
    
    # 合并特征矩阵
    total_test_length = arc_test_seg_length + norm_test_seg_length
    test_matrix_data = np.zeros((total_test_length, feature_length))
    test_matrix_data[:arc_test_seg_length] = arc_test_seg_data
    test_matrix_data[arc_test_seg_length:] = norm_test_seg_data
    
    # 创建标签
    label_test = np.zeros(total_test_length)
    label_test[:arc_test_seg_length] = 1
    
    # 输出处理信息
    print(f'特征矩阵形状: {test_matrix_data.shape}')
    print(f'标签数组形状: {label_test.shape}')
    print(f'故障样本数: {arc_test_seg_length}')
    print(f'正常样本数: {norm_test_seg_length}')
    
    return test_matrix_data, label_test, arc_test_seg_length, norm_test_seg_length

def train_random_forest(train_data, train_labels):
    """
    训练随机森林模型
    :param train_data: 训练数据
    :param train_labels: 训练标签
    :param test_data: 测试数据
    :param test_labels: 测试标签
    :return: 训练好的模型
    """

    
    # 随机森林参数
    tree_nums = 100
    model = RandomForestClassifier(n_estimators=tree_nums, min_samples_leaf=5, 
                                 oob_score=True, n_jobs=-1, random_state=42)
    print('\n开始训练随机森林模型...')
    with tqdm(total=tree_nums, desc='训练随机森林') as pbar:
        model.fit(train_data, train_labels)
        pbar.update(tree_nums)
    return model

if __name__ == "__main__":
    # 显示当前时间
    print(time.strftime('%Y-%m-%d %H:%M:%S'), '开始执行')
    
    # 设置随机种子
    np.random.seed(42)
    
    # 数据路径
    arc_path = r"D:\Dev\AFCI云边协同\数据\processed\ARC"
    normal_path = r"D:\Dev\AFCI云边协同\数据\processed\NOR"

    # 参数设置
    average_width = 5
    feature_length = int(512 / average_width)

    # 处理所有数据
    print("正在处理所有数据...")
    all_data, all_labels, arc_len, norm_len = data_pre_process(arc_path, normal_path, average_width, feature_length)

    print(f"数据处理完成:")
    print(f"  总样本数: {len(all_data)}")
    print(f"  故障样本数: {arc_len}")
    print(f"  正常样本数: {norm_len}")
    print(f"  特征维度: {all_data.shape[1]}")

    # 随机分割数据为训练集(80%)和测试集(20%)
    from sklearn.model_selection import train_test_split

    train_data, test_data, train_labels, test_labels = train_test_split(
        all_data, all_labels,
        test_size=0.2,           # 20%作为测试集
        stratify=all_labels      # 分层抽样，确保训练集和测试集中故障/正常样本比例一致
    )

    print(f"\n数据分割完成:")
    print(f"  训练集样本数: {len(train_data)} ({len(train_data)/len(all_data)*100:.1f}%)")
    print(f"  测试集样本数: {len(test_data)} ({len(test_data)/len(all_data)*100:.1f}%)")
    print(f"  训练集中故障样本数: {np.sum(train_labels == 1)}")
    print(f"  训练集中正常样本数: {np.sum(train_labels == 0)}")
    print(f"  测试集中故障样本数: {np.sum(test_labels == 1)}")
    print(f"  测试集中正常样本数: {np.sum(test_labels == 0)}")

    # 训练模型
    print(f"\n开始训练随机森林模型...")
    model = train_random_forest(train_data, train_labels)

    # 在训练集上预测
    train_pred = model.predict(train_data)
    train_acc = np.mean(train_pred == train_labels)
    print(f"训练集准确率: {train_acc:.4f}")

    # 在测试集上预测（这里保持原有的变量名以兼容后续代码）
    
    # 模型评估
    print(time.strftime('%Y-%m-%d %H:%M:%S'), '模型训练已完成')
    

    test_pred = model.predict(test_data)
    

    test_acc = np.mean(test_pred == test_labels)

    # 计算测试集中的样本数量
    test_arc_len = np.sum(test_labels == 1)  # 测试集中的故障样本数
    test_norm_len = np.sum(test_labels == 0)  # 测试集中的正常样本数

    # 计算混淆矩阵
    tn = np.sum((test_pred == 0) & (test_labels == 0))  # 真阴性
    fp = np.sum((test_pred == 1) & (test_labels == 0))  # 假阳性
    fn = np.sum((test_pred == 0) & (test_labels == 1))  # 假阴性
    tp = np.sum((test_pred == 1) & (test_labels == 1))  # 真阳性

    # 计算误报率和漏报率
    fpr = fp / (fp + tn) if (fp + tn) > 0 else 0  # 误报率 = 假阳性 / (假阳性 + 真阴性)
    fnr = fn / (fn + tp) if (fn + tp) > 0 else 0  # 漏报率 = 假阴性 / (假阴性 + 真阳性)

    # 计算其他评估指标
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0  # 精确率
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0     # 召回率
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0  # F1分数

    # 输出详细结果
    print(f'\n模型评估结果:')
    print(f'=' * 50)
    print(f'数据分布:')
    print(f'  测试集总样本数: {len(test_data)}')
    print(f'  测试集故障样本数: {test_arc_len}')
    print(f'  测试集正常样本数: {test_norm_len}')
    print(f'\n混淆矩阵:')
    print(f'  真阳性(TP): {tp}  假阳性(FP): {fp}')
    print(f'  假阴性(FN): {fn}  真阴性(TN): {tn}')
    print(f'\n性能指标:')
    print(f'  准确率(Accuracy): {test_acc:.4f} ({test_acc:.2%})')
    print(f'  精确率(Precision): {precision:.4f} ({precision:.2%})')
    print(f'  召回率(Recall): {recall:.4f} ({recall:.2%})')
    print(f'  F1分数: {f1_score:.4f}')
    print(f'  误报率(FPR): {fpr:.4f} ({fpr:.2%})')
    print(f'  漏报率(FNR): {fnr:.4f} ({fnr:.2%})')
    
    # 保存模型
    joblib.dump(model, 'random_forest_model.pkl')