package com.naii.afci.service;

import org.junit.jupiter.api.Test;
import smile.classification.RandomForest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单的集成测试，验证核心功能是否正常工作
 */
public class SimpleIntegrationTest {
    
    @Test
    void testBasicFunctionality() {
        // 测试FFT特征提取
        FFTFeatureExtractor extractor = new FFTFeatureExtractor();
        double[] signal = new double[1024];
        
        // 创建简单的测试信号
        for (int i = 0; i < signal.length; i++) {
            signal[i] = Math.sin(2.0 * Math.PI * i / 100.0);
        }
        
        double[] features = extractor.mystft(signal);
        assertNotNull(features);
        assertEquals(TrainingConfiguration.FEATURE_LENGTH, features.length);
        
        // 测试随机森林训练
        RandomForestTrainer trainer = new RandomForestTrainer();
        
        // 创建简单的训练数据 - 使用更多特征以匹配FFT输出
        double[][] trainData = new double[20][TrainingConfiguration.FEATURE_LENGTH];
        int[] trainLabels = new int[20];

        // 创建一些模拟的特征数据
        for (int i = 0; i < 20; i++) {
            for (int j = 0; j < TrainingConfiguration.FEATURE_LENGTH; j++) {
                if (i < 10) {
                    // 故障样本：较大的值
                    trainData[i][j] = Math.random() * 2.0 + 1.0;
                    trainLabels[i] = 1;
                } else {
                    // 正常样本：较小的值
                    trainData[i][j] = Math.random() * 0.5;
                    trainLabels[i] = 0;
                }
            }
        }
        
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);
        assertNotNull(model);
        
        // 测试预测
        int[] predictions = trainer.predict(model, trainData);
        assertNotNull(predictions);
        assertEquals(trainData.length, predictions.length);
        
        // 测试模型评估
        ModelEvaluator evaluator = new ModelEvaluator();
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trainLabels);
        assertNotNull(result);
        assertTrue(result.accuracy >= 0.0 && result.accuracy <= 1.0);
        
        System.out.println("基本功能测试通过！");
        System.out.println("训练数据准确率: " + result.accuracy);
    }
    
    @Test
    void testDataProcessor() {
        DataProcessor processor = new DataProcessor();
        
        // 测试数据分段计算
        double[] data = new double[2500]; // 2.5 * 1024
        int segments = processor.calculateSegments(data, TrainingConfiguration.WINDOW_LENGTH);
        assertEquals(3, segments); // 2个完整段 + 1个补齐段
        
        // 测试分段提取
        for (int i = 0; i < data.length; i++) {
            data[i] = i;
        }
        
        double[] segment0 = processor.extractSegment(data, 0, TrainingConfiguration.WINDOW_LENGTH);
        assertEquals(TrainingConfiguration.WINDOW_LENGTH, segment0.length);
        assertEquals(0.0, segment0[0], 1e-10);
        
        double[] segment1 = processor.extractSegment(data, 1, TrainingConfiguration.WINDOW_LENGTH);
        assertEquals(TrainingConfiguration.WINDOW_LENGTH, segment1.length);
        assertEquals(1024.0, segment1[0], 1e-10);
        
        // 最后一段（向前补齐）
        double[] segment2 = processor.extractSegment(data, 2, TrainingConfiguration.WINDOW_LENGTH);
        assertEquals(TrainingConfiguration.WINDOW_LENGTH, segment2.length);
        assertEquals(1476.0, segment2[0], 1e-10); // 2500 - 1024 = 1476
        
        System.out.println("数据处理器测试通过！");
    }
    
    @Test
    void testTrainTestSplit() {
        RandomForestTrainer trainer = new RandomForestTrainer();
        
        // 创建测试数据
        double[][] data = new double[100][10];
        int[] labels = new int[100];
        
        for (int i = 0; i < 100; i++) {
            for (int j = 0; j < 10; j++) {
                data[i][j] = Math.random();
            }
            labels[i] = i % 2; // 交替标签
        }
        
        Object[] result = trainer.trainTestSplit(data, labels, 0.2, 42);
        
        double[][] trainData = (double[][]) result[0];
        double[][] testData = (double[][]) result[1];
        int[] trainLabels = (int[]) result[2];
        int[] testLabels = (int[]) result[3];
        
        assertEquals(80, trainData.length);
        assertEquals(20, testData.length);
        assertEquals(80, trainLabels.length);
        assertEquals(20, testLabels.length);
        
        System.out.println("数据分割测试通过！");
    }
}
