package com.naii.afci.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import smile.classification.RandomForest;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RandomForestTrainer类的单元测试
 */
public class RandomForestTrainerTest {
    
    private RandomForestTrainer trainer;
    
    @BeforeEach
    void setUp() {
        trainer = new RandomForestTrainer();
    }
    
    @Test
    void testTrainTestSplit() {
        // 创建测试数据
        double[][] data = {
            {1.0, 2.0}, {3.0, 4.0}, {5.0, 6.0}, {7.0, 8.0}, 
            {9.0, 10.0}, {11.0, 12.0}, {13.0, 14.0}, {15.0, 16.0}
        };
        int[] labels = {0, 1, 0, 1, 0, 1, 0, 1};
        
        Object[] result = trainer.trainTestSplit(data, labels, 0.25, 42);
        
        double[][] trainData = (double[][]) result[0];
        double[][] testData = (double[][]) result[1];
        int[] trainLabels = (int[]) result[2];
        int[] testLabels = (int[]) result[3];
        
        // 验证分割比例
        assertEquals(6, trainData.length); // 75%
        assertEquals(2, testData.length);  // 25%
        assertEquals(6, trainLabels.length);
        assertEquals(2, testLabels.length);
        
        // 验证总数据量保持不变
        assertEquals(data.length, trainData.length + testData.length);
        
        // 验证数据完整性（所有原始数据都应该出现在训练集或测试集中）
        Set<String> originalDataSet = new HashSet<>();
        for (double[] row : data) {
            originalDataSet.add(Arrays.toString(row));
        }
        
        Set<String> splitDataSet = new HashSet<>();
        for (double[] row : trainData) {
            splitDataSet.add(Arrays.toString(row));
        }
        for (double[] row : testData) {
            splitDataSet.add(Arrays.toString(row));
        }
        
        assertEquals(originalDataSet, splitDataSet);
    }
    
    @Test
    void testTrainTestSplitReproducibility() {
        // 测试相同随机种子产生相同结果
        double[][] data = {
            {1.0, 2.0}, {3.0, 4.0}, {5.0, 6.0}, {7.0, 8.0}
        };
        int[] labels = {0, 1, 0, 1};
        
        Object[] result1 = trainer.trainTestSplit(data, labels, 0.5, 42);
        Object[] result2 = trainer.trainTestSplit(data, labels, 0.5, 42);
        
        double[][] trainData1 = (double[][]) result1[0];
        double[][] trainData2 = (double[][]) result2[0];
        
        // 验证相同随机种子产生相同结果
        assertEquals(trainData1.length, trainData2.length);
        for (int i = 0; i < trainData1.length; i++) {
            assertArrayEquals(trainData1[i], trainData2[i], 1e-10);
        }
    }
    
    @Test
    void testTrainRandomForest() {
        // 创建简单的线性可分数据
        double[][] trainData = {
            {1.0, 1.0}, {2.0, 2.0}, {3.0, 3.0}, {4.0, 4.0},  // 类别1
            {-1.0, -1.0}, {-2.0, -2.0}, {-3.0, -3.0}, {-4.0, -4.0}  // 类别0
        };
        int[] trainLabels = {1, 1, 1, 1, 0, 0, 0, 0};
        
        // 训练模型
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);
        
        assertNotNull(model);
        
        // 测试预测功能
        int[] predictions = trainer.predict(model, trainData);
        assertEquals(trainData.length, predictions.length);
        
        // 验证预测结果在合理范围内
        for (int prediction : predictions) {
            assertTrue(prediction == 0 || prediction == 1);
        }
    }
    
    @Test
    void testPredictConsistency() {
        // 创建测试数据
        double[][] trainData = {
            {1.0, 1.0}, {2.0, 2.0}, {-1.0, -1.0}, {-2.0, -2.0}
        };
        int[] trainLabels = {1, 1, 0, 0};
        
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);
        
        // 测试相同输入产生相同输出
        double[][] testData = {{1.5, 1.5}, {-1.5, -1.5}};
        int[] predictions1 = trainer.predict(model, testData);
        int[] predictions2 = trainer.predict(model, testData);
        
        assertArrayEquals(predictions1, predictions2);
    }
    
    @Test
    void testTrainWithSingleClass() {
        // 测试单一类别的情况
        double[][] trainData = {
            {1.0, 1.0}, {2.0, 2.0}, {3.0, 3.0}, {4.0, 4.0}
        };
        int[] trainLabels = {1, 1, 1, 1}; // 全部是类别1
        
        // 应该能够训练，但可能会有警告
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);
        assertNotNull(model);
        
        // 预测应该全部返回类别1
        int[] predictions = trainer.predict(model, trainData);
        for (int prediction : predictions) {
            assertEquals(1, prediction);
        }
    }
    
    @Test
    void testGetOOBScore() {
        double[][] trainData = {
            {1.0, 1.0}, {2.0, 2.0}, {-1.0, -1.0}, {-2.0, -2.0}
        };
        int[] trainLabels = {1, 1, 0, 0};
        
        RandomForest model = trainer.trainRandomForest(trainData, trainLabels);
        
        // 目前实现返回-1表示不可用
        double oobScore = trainer.getOOBScore(model);
        assertEquals(-1.0, oobScore, 1e-10);
    }
    
    @Test
    void testTrainTestSplitEdgeCases() {
        // 测试边界情况
        double[][] data = {{1.0, 2.0}, {3.0, 4.0}};
        int[] labels = {0, 1};
        
        // 测试50%分割
        Object[] result = trainer.trainTestSplit(data, labels, 0.5, 42);
        double[][] trainData = (double[][]) result[0];
        double[][] testData = (double[][]) result[1];
        
        assertEquals(1, trainData.length);
        assertEquals(1, testData.length);
        
        // 测试极小分割比例
        Object[] result2 = trainer.trainTestSplit(data, labels, 0.1, 42);
        double[][] trainData2 = (double[][]) result2[0];
        double[][] testData2 = (double[][]) result2[1];
        
        assertEquals(2, trainData2.length); // 至少保留一个测试样本
        assertEquals(0, testData2.length);
    }
}
