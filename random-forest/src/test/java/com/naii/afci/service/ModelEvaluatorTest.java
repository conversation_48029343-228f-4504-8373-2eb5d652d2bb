package com.naii.afci.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ModelEvaluator类的单元测试
 */
public class ModelEvaluatorTest {
    
    private ModelEvaluator evaluator;
    
    @BeforeEach
    void setUp() {
        evaluator = new ModelEvaluator();
    }
    
    @Test
    void testPerfectPrediction() {
        // 测试完美预测
        int[] predictions = {1, 1, 0, 0, 1, 0};
        int[] trueLabels = {1, 1, 0, 0, 1, 0};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        assertEquals(3, result.truePositive);
        assertEquals(0, result.falsePositive);
        assertEquals(3, result.trueNegative);
        assertEquals(0, result.falseNegative);
        assertEquals(1.0, result.accuracy, 1e-10);
        assertEquals(1.0, result.precision, 1e-10);
        assertEquals(1.0, result.recall, 1e-10);
        assertEquals(1.0, result.f1Score, 1e-10);
        assertEquals(0.0, result.falsePositiveRate, 1e-10);
        assertEquals(0.0, result.falseNegativeRate, 1e-10);
    }
    
    @Test
    void testWorstPrediction() {
        // 测试最差预测（完全相反）
        int[] predictions = {0, 0, 1, 1, 0, 1};
        int[] trueLabels = {1, 1, 0, 0, 1, 0};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        assertEquals(0, result.truePositive);
        assertEquals(3, result.falsePositive);
        assertEquals(0, result.trueNegative);
        assertEquals(3, result.falseNegative);
        assertEquals(0.0, result.accuracy, 1e-10);
        assertEquals(0.0, result.precision, 1e-10);
        assertEquals(0.0, result.recall, 1e-10);
        assertEquals(0.0, result.f1Score, 1e-10);
        assertEquals(1.0, result.falsePositiveRate, 1e-10);
        assertEquals(1.0, result.falseNegativeRate, 1e-10);
    }
    
    @Test
    void testMixedPrediction() {
        // 测试混合预测结果
        int[] predictions = {1, 0, 0, 1, 1, 0, 1, 0};
        int[] trueLabels = {1, 1, 0, 0, 1, 0, 0, 1};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        // 手动计算期望值
        // TP: predictions=1 && trueLabels=1 -> 位置0,4 = 2
        // FP: predictions=1 && trueLabels=0 -> 位置3,6 = 2  
        // TN: predictions=0 && trueLabels=0 -> 位置2,5 = 2
        // FN: predictions=0 && trueLabels=1 -> 位置1,7 = 2
        
        assertEquals(2, result.truePositive);
        assertEquals(2, result.falsePositive);
        assertEquals(2, result.trueNegative);
        assertEquals(2, result.falseNegative);
        assertEquals(0.5, result.accuracy, 1e-10); // (2+2)/8 = 0.5
        assertEquals(0.5, result.precision, 1e-10); // 2/(2+2) = 0.5
        assertEquals(0.5, result.recall, 1e-10); // 2/(2+2) = 0.5
        assertEquals(0.5, result.f1Score, 1e-10); // 2*0.5*0.5/(0.5+0.5) = 0.5
        assertEquals(0.5, result.falsePositiveRate, 1e-10); // 2/(2+2) = 0.5
        assertEquals(0.5, result.falseNegativeRate, 1e-10); // 2/(2+2) = 0.5
    }
    
    @Test
    void testAllPositivePredictions() {
        // 测试全部预测为正类
        int[] predictions = {1, 1, 1, 1};
        int[] trueLabels = {1, 1, 0, 0};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        assertEquals(2, result.truePositive);
        assertEquals(2, result.falsePositive);
        assertEquals(0, result.trueNegative);
        assertEquals(0, result.falseNegative);
        assertEquals(0.5, result.accuracy, 1e-10);
        assertEquals(0.5, result.precision, 1e-10);
        assertEquals(1.0, result.recall, 1e-10);
        assertEquals(2.0/3.0, result.f1Score, 1e-10);
        assertEquals(1.0, result.falsePositiveRate, 1e-10);
        assertEquals(0.0, result.falseNegativeRate, 1e-10);
    }
    
    @Test
    void testAllNegativePredictions() {
        // 测试全部预测为负类
        int[] predictions = {0, 0, 0, 0};
        int[] trueLabels = {1, 1, 0, 0};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        assertEquals(0, result.truePositive);
        assertEquals(0, result.falsePositive);
        assertEquals(2, result.trueNegative);
        assertEquals(2, result.falseNegative);
        assertEquals(0.5, result.accuracy, 1e-10);
        assertEquals(0.0, result.precision, 1e-10);
        assertEquals(0.0, result.recall, 1e-10);
        assertEquals(0.0, result.f1Score, 1e-10);
        assertEquals(0.0, result.falsePositiveRate, 1e-10);
        assertEquals(1.0, result.falseNegativeRate, 1e-10);
    }
    
    @Test
    void testCalculateAccuracy() {
        int[] predictions = {1, 0, 1, 0, 1};
        int[] trueLabels = {1, 0, 0, 0, 1};
        
        double accuracy = evaluator.calculateAccuracy(predictions, trueLabels);
        assertEquals(0.8, accuracy, 1e-10); // 4/5 = 0.8
    }
    
    @Test
    void testMismatchedArrays() {
        int[] predictions = {1, 0, 1};
        int[] trueLabels = {1, 0};
        
        assertThrows(IllegalArgumentException.class, () -> {
            evaluator.evaluate(predictions, trueLabels);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            evaluator.calculateAccuracy(predictions, trueLabels);
        });
    }
    
    @Test
    void testSampleCounting() {
        int[] predictions = {1, 0, 1, 0, 1, 0};
        int[] trueLabels = {1, 1, 0, 0, 1, 0};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        assertEquals(6, result.totalSamples);
        assertEquals(3, result.faultSamples); // 标签为1的样本数
        assertEquals(3, result.normalSamples); // 标签为0的样本数
    }
}
