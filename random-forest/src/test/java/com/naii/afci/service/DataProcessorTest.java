package com.naii.afci.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataProcessor类的单元测试
 */
public class DataProcessorTest {
    
    private DataProcessor dataProcessor;
    
    @BeforeEach
    void setUp() {
        dataProcessor = new DataProcessor();
    }
    
    @Test
    void testScanDir(@TempDir Path tempDir) throws IOException {
        // 创建测试文件
        Path file1 = tempDir.resolve("test1.csv");
        Path file2 = tempDir.resolve("test2.csv");
        Path subDir = tempDir.resolve("subdir");
        Files.createDirectory(subDir);
        Path file3 = subDir.resolve("test3.csv");
        
        Files.createFile(file1);
        Files.createFile(file2);
        Files.createFile(file3);
        
        // 测试扫描目录
        List<String> files = dataProcessor.scanDir(tempDir.toString());
        
        assertEquals(3, files.size());
        assertTrue(files.stream().anyMatch(f -> f.endsWith("test1.csv")));
        assertTrue(files.stream().anyMatch(f -> f.endsWith("test2.csv")));
        assertTrue(files.stream().anyMatch(f -> f.endsWith("test3.csv")));
    }
    
    @Test
    void testScanDirNonExistent() {
        assertThrows(IllegalArgumentException.class, () -> {
            dataProcessor.scanDir("/non/existent/path");
        });
    }
    
    @Test
    void testLoadCsvRobust(@TempDir Path tempDir) throws IOException {
        // 创建测试CSV文件
        Path csvFile = tempDir.resolve("test.csv");
        String csvContent = "1.0,2.0,3.0\n4.0,5.0,6.0\n\"7.0\",\"8.0\",\"9.0\"";
        Files.write(csvFile, csvContent.getBytes());
        
        // 测试加载CSV
        double[] data = dataProcessor.loadCsvRobust(csvFile.toString());
        
        assertNotNull(data);
        assertEquals(9, data.length);
        assertArrayEquals(new double[]{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0}, data, 1e-10);
    }
    
    @Test
    void testLoadCsvRobustWithInvalidData(@TempDir Path tempDir) throws IOException {
        // 创建包含无效数据的CSV文件
        Path csvFile = tempDir.resolve("test_invalid.csv");
        String csvContent = "1.0,invalid,3.0\n4.0,5.0,another_invalid\n7.0,8.0,9.0";
        Files.write(csvFile, csvContent.getBytes());
        
        // 测试加载CSV（应该跳过无效数据）
        double[] data = dataProcessor.loadCsvRobust(csvFile.toString());
        
        assertNotNull(data);
        assertEquals(7, data.length); // 跳过了2个无效值
        assertArrayEquals(new double[]{1.0, 3.0, 4.0, 5.0, 7.0, 8.0, 9.0}, data, 1e-10);
    }
    
    @Test
    void testCalculateSegments() {
        // 测试完整段
        double[] data1 = new double[2048]; // 2 * 1024
        int segments1 = dataProcessor.calculateSegments(data1, 1024);
        assertEquals(2, segments1);
        
        // 测试有余数的情况
        double[] data2 = new double[1500]; // 1024 + 476
        int segments2 = dataProcessor.calculateSegments(data2, 1024);
        assertEquals(2, segments2); // 1个完整段 + 1个补齐段
        
        // 测试数据长度小于窗口长度
        double[] data3 = new double[500];
        int segments3 = dataProcessor.calculateSegments(data3, 1024);
        assertEquals(0, segments3);
    }
    
    @Test
    void testExtractSegment() {
        double[] data = new double[1500];
        for (int i = 0; i < data.length; i++) {
            data[i] = i;
        }
        
        // 测试提取第一个完整段
        double[] segment1 = dataProcessor.extractSegment(data, 0, 1024);
        assertEquals(1024, segment1.length);
        assertEquals(0.0, segment1[0], 1e-10);
        assertEquals(1023.0, segment1[1023], 1e-10);
        
        // 测试提取最后一个段（向前补齐）
        double[] segment2 = dataProcessor.extractSegment(data, 1, 1024);
        assertEquals(1024, segment2.length);
        assertEquals(476.0, segment2[0], 1e-10); // 1500 - 1024 = 476
        assertEquals(1499.0, segment2[1023], 1e-10);
    }
    
    @Test
    void testExtractSegmentInvalidIndex() {
        double[] data = new double[1500];
        
        assertThrows(IllegalArgumentException.class, () -> {
            dataProcessor.extractSegment(data, 5, 1024); // 超出范围
        });
    }
}
