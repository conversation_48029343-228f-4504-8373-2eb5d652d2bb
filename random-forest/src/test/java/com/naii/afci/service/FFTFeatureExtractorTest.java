package com.naii.afci.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FFTFeatureExtractor类的单元测试
 */
public class FFTFeatureExtractorTest {
    
    private FFTFeatureExtractor extractor;
    
    @BeforeEach
    void setUp() {
        extractor = new FFTFeatureExtractor();
    }
    
    @Test
    void testHanningWindow() {
        // 测试汉宁窗生成
        double[] window = extractor.hanningWindow(4);
        
        assertEquals(4, window.length);
        assertEquals(0.0, window[0], 1e-10); // 第一个值应该是0
        assertEquals(0.0, window[3], 1e-10); // 最后一个值应该接近0
        assertTrue(window[1] > 0); // 中间值应该大于0
        assertTrue(window[2] > 0);
    }
    
    @Test
    void testComplexOperations() {
        FFTFeatureExtractor.Complex c1 = new FFTFeatureExtractor.Complex(1.0, 2.0);
        FFTFeatureExtractor.Complex c2 = new FFTFeatureExtractor.Complex(3.0, 4.0);
        
        // 测试加法
        FFTFeatureExtractor.Complex sum = c1.add(c2);
        assertEquals(4.0, sum.real, 1e-10);
        assertEquals(6.0, sum.imag, 1e-10);
        
        // 测试减法
        FFTFeatureExtractor.Complex diff = c1.subtract(c2);
        assertEquals(-2.0, diff.real, 1e-10);
        assertEquals(-2.0, diff.imag, 1e-10);
        
        // 测试乘法
        FFTFeatureExtractor.Complex product = c1.multiply(c2);
        assertEquals(-5.0, product.real, 1e-10); // 1*3 - 2*4 = -5
        assertEquals(10.0, product.imag, 1e-10); // 1*4 + 2*3 = 10
        
        // 测试幅度
        double magnitude = c1.magnitude();
        assertEquals(Math.sqrt(5.0), magnitude, 1e-10);
    }
    
    @Test
    void testFFTBasic() {
        // 测试基本FFT功能
        FFTFeatureExtractor.Complex[] input = {
            new FFTFeatureExtractor.Complex(1.0, 0.0),
            new FFTFeatureExtractor.Complex(0.0, 0.0),
            new FFTFeatureExtractor.Complex(0.0, 0.0),
            new FFTFeatureExtractor.Complex(0.0, 0.0)
        };
        
        FFTFeatureExtractor.Complex[] result = extractor.fft(input);
        
        assertEquals(4, result.length);
        // 对于单位脉冲，FFT结果应该是常数
        for (FFTFeatureExtractor.Complex c : result) {
            assertEquals(1.0, c.real, 1e-10);
            assertEquals(0.0, c.imag, 1e-10);
        }
    }
    
    @Test
    void testFFTSingleElement() {
        // 测试单元素FFT
        FFTFeatureExtractor.Complex[] input = {
            new FFTFeatureExtractor.Complex(5.0, 3.0)
        };
        
        FFTFeatureExtractor.Complex[] result = extractor.fft(input);
        
        assertEquals(1, result.length);
        assertEquals(5.0, result[0].real, 1e-10);
        assertEquals(3.0, result[0].imag, 1e-10);
    }
    
    @Test
    void testMystft() {
        // 创建测试信号：1024个点的正弦波
        double[] signal = new double[1024];
        double frequency = 10.0; // 10 Hz
        double sampleRate = 250000.0; // 250 kHz
        
        for (int i = 0; i < signal.length; i++) {
            signal[i] = Math.sin(2.0 * Math.PI * frequency * i / sampleRate);
        }
        
        // 计算FFT特征
        double[] features = extractor.mystft(signal);
        
        // 验证特征长度
        assertEquals(TrainingConfiguration.FEATURE_LENGTH, features.length);
        
        // 验证特征值都是有限的数值
        for (double feature : features) {
            assertTrue(Double.isFinite(feature));
            assertTrue(feature >= 0.0); // 归一化后应该都是非负数
        }
    }
    
    @Test
    void testMystftWithZeroSignal() {
        // 测试零信号
        double[] signal = new double[1024];
        // 所有值都是0
        
        double[] features = extractor.mystft(signal);
        
        assertEquals(TrainingConfiguration.FEATURE_LENGTH, features.length);
        
        // 对于零信号，所有特征应该都是0
        for (double feature : features) {
            assertEquals(0.0, feature, 1e-10);
        }
    }
    
    @Test
    void testMystftWithConstantSignal() {
        // 测试常数信号
        double[] signal = new double[1024];
        for (int i = 0; i < signal.length; i++) {
            signal[i] = 1.0;
        }
        
        double[] features = extractor.mystft(signal);
        
        assertEquals(TrainingConfiguration.FEATURE_LENGTH, features.length);
        
        // 验证特征值都是有限的数值
        for (double feature : features) {
            assertTrue(Double.isFinite(feature));
        }
        
        // 对于常数信号，第一个特征应该是最大的（DC分量）
        assertTrue(features[0] >= features[1]);
    }
    
    @Test
    void testMystftReproducibility() {
        // 测试相同输入产生相同输出
        double[] signal = new double[1024];
        for (int i = 0; i < signal.length; i++) {
            signal[i] = Math.sin(2.0 * Math.PI * i / 100.0);
        }
        
        double[] features1 = extractor.mystft(signal);
        double[] features2 = extractor.mystft(signal);
        
        assertArrayEquals(features1, features2, 1e-10);
    }
}
