package com.naii.afci.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import smile.classification.RandomForest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Java版本与Python版本一致性测试
 * 验证Java实现与Python实现产生相同的结果
 */
public class PythonJavaConsistencyTest {
    
    private RandomForestService service;
    private DataProcessor dataProcessor;
    private FFTFeatureExtractor fftExtractor;
    private RandomForestTrainer trainer;
    private ModelEvaluator evaluator;
    
    @BeforeEach
    void setUp() {
        service = new RandomForestService();
        dataProcessor = new DataProcessor();
        fftExtractor = new FFTFeatureExtractor();
        trainer = new RandomForestTrainer();
        evaluator = new ModelEvaluator();
    }
    
    @Test
    void testFFTFeatureConsistency() {
        // 测试FFT特征提取与Python版本的一致性
        // 使用已知的测试信号
        double[] signal = createTestSignal();
        
        double[] features = fftExtractor.mystft(signal);
        
        // 验证特征长度与Python版本一致
        assertEquals(TrainingConfiguration.FEATURE_LENGTH, features.length);
        
        // 验证特征值的基本属性
        for (double feature : features) {
            assertTrue(Double.isFinite(feature), "特征值应该是有限数");
            assertTrue(feature >= 0.0, "归一化后的特征值应该非负");
        }
        
        // 验证特征的数值范围合理
        double maxFeature = java.util.Arrays.stream(features).max().orElse(0.0);
        double minFeature = java.util.Arrays.stream(features).min().orElse(0.0);
        assertTrue(maxFeature >= minFeature, "最大值应该大于等于最小值");
    }
    
    @Test
    void testDataProcessingConsistency(@TempDir Path tempDir) throws IOException {
        // 创建测试数据文件，模拟Python版本的数据格式
        createTestDataFiles(tempDir);
        
        // 测试文件扫描
        var arcFiles = dataProcessor.scanDir(tempDir.resolve("arc").toString());
        var normalFiles = dataProcessor.scanDir(tempDir.resolve("normal").toString());
        
        assertFalse(arcFiles.isEmpty(), "应该找到故障数据文件");
        assertFalse(normalFiles.isEmpty(), "应该找到正常数据文件");
        
        // 测试CSV加载
        double[] data = dataProcessor.loadCsvRobust(arcFiles.get(0));
        assertNotNull(data);
        assertTrue(data.length > 0, "应该加载到数据");
        
        // 测试数据分段
        int segments = dataProcessor.calculateSegments(data, TrainingConfiguration.WINDOW_LENGTH);
        assertTrue(segments >= 0, "分段数应该非负");
    }
    
    @Test
    void testRandomForestParameterConsistency() {
        // 验证随机森林参数与Python版本一致
        assertEquals(100, TrainingConfiguration.N_ESTIMATORS);
        assertEquals(5, TrainingConfiguration.MIN_SAMPLES_LEAF);
        assertEquals(42, TrainingConfiguration.RANDOM_STATE);
        assertEquals(0.2, TrainingConfiguration.TEST_SIZE);
    }
    
    @Test
    void testTrainTestSplitConsistency() {
        // 测试数据分割的一致性
        double[][] data = createTestFeatureMatrix(100, TrainingConfiguration.FEATURE_LENGTH);
        int[] labels = createTestLabels(100);
        
        Object[] result = trainer.trainTestSplit(data, labels, 
                                               TrainingConfiguration.TEST_SIZE, 
                                               TrainingConfiguration.RANDOM_STATE);
        
        double[][] trainData = (double[][]) result[0];
        double[][] testData = (double[][]) result[1];
        int[] trainLabels = (int[]) result[2];
        int[] testLabels = (int[]) result[3];
        
        // 验证分割比例
        int expectedTestSize = (int) (data.length * TrainingConfiguration.TEST_SIZE);
        int expectedTrainSize = data.length - expectedTestSize;
        
        assertEquals(expectedTrainSize, trainData.length);
        assertEquals(expectedTestSize, testData.length);
        assertEquals(expectedTrainSize, trainLabels.length);
        assertEquals(expectedTestSize, testLabels.length);
    }
    
    @Test
    void testModelEvaluationConsistency() {
        // 测试模型评估指标计算的一致性
        int[] predictions = {1, 1, 0, 0, 1, 0, 1, 0};
        int[] trueLabels = {1, 0, 0, 1, 1, 0, 0, 1};
        
        ModelEvaluator.EvaluationResult result = evaluator.evaluate(predictions, trueLabels);
        
        // 手动验证混淆矩阵
        // TP: 位置0,4 = 2
        // FP: 位置1,6 = 2
        // TN: 位置2,5 = 2
        // FN: 位置3,7 = 2
        
        assertEquals(2, result.truePositive);
        assertEquals(2, result.falsePositive);
        assertEquals(2, result.trueNegative);
        assertEquals(2, result.falseNegative);
        
        // 验证计算的指标
        assertEquals(0.5, result.accuracy, 1e-10);
        assertEquals(0.5, result.precision, 1e-10);
        assertEquals(0.5, result.recall, 1e-10);
        assertEquals(0.5, result.f1Score, 1e-10);
    }
    
    @Test
    void testEndToEndConsistency(@TempDir Path tempDir) throws IOException {
        // 端到端测试，验证整个流程的一致性
        createTestDataFiles(tempDir);
        
        String arcPath = tempDir.resolve("arc").toString();
        String normalPath = tempDir.resolve("normal").toString();
        
        // 执行数据预处理
        Object[] preprocessResult = service.dataPreProcess(arcPath, normalPath);
        double[][] allData = (double[][]) preprocessResult[0];
        int[] allLabels = (int[]) preprocessResult[1];
        
        assertNotNull(allData);
        assertNotNull(allLabels);
        assertEquals(allData.length, allLabels.length);
        
        // 验证特征维度
        if (allData.length > 0) {
            assertEquals(TrainingConfiguration.FEATURE_LENGTH, allData[0].length);
        }
        
        // 验证标签值
        for (int label : allLabels) {
            assertTrue(label == TrainingConfiguration.FAULT_LABEL || 
                      label == TrainingConfiguration.NORMAL_LABEL);
        }
    }
    
    /**
     * 创建测试信号
     */
    private double[] createTestSignal() {
        double[] signal = new double[TrainingConfiguration.WINDOW_LENGTH];
        double frequency = 50.0; // 50 Hz
        double sampleRate = TrainingConfiguration.SAMPLING_FREQUENCY;
        
        for (int i = 0; i < signal.length; i++) {
            signal[i] = Math.sin(2.0 * Math.PI * frequency * i / sampleRate) + 
                       0.1 * Math.sin(2.0 * Math.PI * frequency * 3 * i / sampleRate);
        }
        
        return signal;
    }
    
    /**
     * 创建测试数据文件
     */
    private void createTestDataFiles(Path tempDir) throws IOException {
        Path arcDir = tempDir.resolve("arc");
        Path normalDir = tempDir.resolve("normal");
        
        Files.createDirectories(arcDir);
        Files.createDirectories(normalDir);
        
        // 创建故障数据文件
        for (int i = 0; i < 3; i++) {
            Path arcFile = arcDir.resolve("arc_" + i + ".csv");
            createTestCsvFile(arcFile, 1500, true); // 故障信号
        }
        
        // 创建正常数据文件
        for (int i = 0; i < 3; i++) {
            Path normalFile = normalDir.resolve("normal_" + i + ".csv");
            createTestCsvFile(normalFile, 1500, false); // 正常信号
        }
    }
    
    /**
     * 创建测试CSV文件
     */
    private void createTestCsvFile(Path filePath, int length, boolean isFault) throws IOException {
        StringBuilder content = new StringBuilder();
        Random random = new Random(42);
        
        for (int i = 0; i < length; i++) {
            double value;
            if (isFault) {
                // 故障信号：包含更多噪声和异常值
                value = Math.sin(2.0 * Math.PI * i / 100.0) + 
                       random.nextGaussian() * 0.5 + 
                       (random.nextDouble() > 0.9 ? random.nextGaussian() * 2.0 : 0.0);
            } else {
                // 正常信号：较为平稳
                value = Math.sin(2.0 * Math.PI * i / 100.0) + 
                       random.nextGaussian() * 0.1;
            }
            content.append(value);
            if (i < length - 1) {
                content.append("\n");
            }
        }
        
        Files.write(filePath, content.toString().getBytes());
    }
    
    /**
     * 创建测试特征矩阵
     */
    private double[][] createTestFeatureMatrix(int samples, int features) {
        double[][] matrix = new double[samples][features];
        Random random = new Random(42);
        
        for (int i = 0; i < samples; i++) {
            for (int j = 0; j < features; j++) {
                matrix[i][j] = random.nextGaussian();
            }
        }
        
        return matrix;
    }
    
    /**
     * 创建测试标签
     */
    private int[] createTestLabels(int samples) {
        int[] labels = new int[samples];
        Random random = new Random(42);
        
        for (int i = 0; i < samples; i++) {
            labels[i] = random.nextBoolean() ? 
                       TrainingConfiguration.FAULT_LABEL : 
                       TrainingConfiguration.NORMAL_LABEL;
        }
        
        return labels;
    }
}
