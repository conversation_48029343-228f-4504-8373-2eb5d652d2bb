<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.naii</groupId>
        <artifactId>afci</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.naii.afci</groupId>
    <version>0.0.1-SNAPSHOT</version>

    <artifactId>random-forest</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.haifengl</groupId>
            <artifactId>smile-core</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.haifengl</groupId>
            <artifactId>smile-data</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.haifengl</groupId>
            <artifactId>smile-io</artifactId>
            <version>2.6.0</version>
        </dependency>

        <!-- JUnit 5 测试依赖 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.9.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>